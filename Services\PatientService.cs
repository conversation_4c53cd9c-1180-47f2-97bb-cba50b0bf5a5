using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using AutoMapper;
using ZyOnlineApi.Data;
using ZyOnlineApi.DTOs;
using ZyOnlineApi.Models;

namespace ZyOnlineApi.Services
{
    public class PatientService : IPatientService
    {
        private readonly ApplicationDbContext _context;
        private readonly IMapper _mapper;

        public PatientService(ApplicationDbContext context, IMapper mapper)
        {
            _context = context;
            _mapper = mapper;
        }

        public async Task<List<PatientDto>> GetPatientsByWechatOpenIdAsync(string wechatOpenId)
        {
            var patients = await _context.Patients
                .Where(p => p.WechatOpenId == wechatOpenId && p.IsActive)
                .OrderByDescending(p => p.CreatedAt)
                .ToListAsync();

            return _mapper.Map<List<PatientDto>>(patients);
        }

        public async Task<PatientDto> GetPatientByIdAsync(int id)
        {
            var patient = await _context.Patients
                .FirstOrDefaultAsync(p => p.Id == id && p.IsActive);

            return _mapper.Map<PatientDto>(patient);
        }

        public async Task<PatientDto> CreatePatientAsync(CreatePatientDto createPatientDto, string wechatOpenId)
        {
            // 检查身份证号是否已存在
            var existingPatient = await _context.Patients
                .FirstOrDefaultAsync(p => p.IdCard == createPatientDto.IdCard && p.IsActive);

            if (existingPatient != null)
            {
                throw new InvalidOperationException("该身份证号已被注册");
            }

            var patient = _mapper.Map<Patient>(createPatientDto);
            patient.WechatOpenId = wechatOpenId;
            patient.CreatedAt = DateTime.Now;
            patient.UpdatedAt = DateTime.Now;

            _context.Patients.Add(patient);
            await _context.SaveChangesAsync();

            return _mapper.Map<PatientDto>(patient);
        }

        public async Task<PatientDto> UpdatePatientAsync(int id, UpdatePatientDto updatePatientDto, string wechatOpenId)
        {
            var patient = await _context.Patients
                .FirstOrDefaultAsync(p => p.Id == id && p.WechatOpenId == wechatOpenId && p.IsActive);

            if (patient == null)
            {
                throw new InvalidOperationException("就诊人不存在或无权限修改");
            }

            // 检查身份证号是否被其他人使用
            var existingPatient = await _context.Patients
                .FirstOrDefaultAsync(p => p.IdCard == updatePatientDto.IdCard && p.Id != id && p.IsActive);

            if (existingPatient != null)
            {
                throw new InvalidOperationException("该身份证号已被其他人注册");
            }

            _mapper.Map(updatePatientDto, patient);
            patient.UpdatedAt = DateTime.Now;

            await _context.SaveChangesAsync();

            return _mapper.Map<PatientDto>(patient);
        }

        public async Task<bool> DeletePatientAsync(int id, string wechatOpenId)
        {
            var patient = await _context.Patients
                .FirstOrDefaultAsync(p => p.Id == id && p.WechatOpenId == wechatOpenId && p.IsActive);

            if (patient == null)
            {
                return false;
            }

            // 检查是否有未完成的预约
            var hasActiveAppointments = await _context.Appointments
                .AnyAsync(a => a.PatientId == id && a.Status == "待检查");

            if (hasActiveAppointments)
            {
                throw new InvalidOperationException("该就诊人有未完成的预约，无法删除");
            }

            patient.IsActive = false;
            patient.UpdatedAt = DateTime.Now;

            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> ValidatePatientOwnershipAsync(int patientId, string wechatOpenId)
        {
            return await _context.Patients
                .AnyAsync(p => p.Id == patientId && p.WechatOpenId == wechatOpenId && p.IsActive);
        }
    }
}
