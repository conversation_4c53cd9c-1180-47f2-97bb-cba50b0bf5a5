# 智影在线服务前端部署指南

## 🚀 快速开始

### 1. 环境准备
```bash
# 安装 Node.js 16+
node --version
npm --version

# 克隆项目
git clone <repository-url>
cd frontend

# 安装依赖
npm install
```

### 2. 开发环境启动
```bash
# 启动开发服务器
npm run dev

# 访问地址
# 患者端: http://localhost:3000/mobile.html
# 后台管理端: http://localhost:3000/admin.html
```

### 3. 生产环境构建
```bash
# 构建生产版本
npm run build

# 预览构建结果
npm run preview
```

## 📦 部署方案

### 方案一：Nginx 静态部署

#### 1. 构建项目
```bash
npm run build
```

#### 2. Nginx 配置
```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /var/www/zyonline-frontend/dist;
    index index.html;

    # 患者端路由
    location /mobile {
        try_files $uri $uri/ /mobile.html;
        add_header Cache-Control "no-cache, no-store, must-revalidate";
    }

    # 后台管理端路由
    location /admin {
        try_files $uri $uri/ /admin.html;
        add_header Cache-Control "no-cache, no-store, must-revalidate";
    }

    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # API 代理到后端
    location /api {
        proxy_pass http://localhost:5001;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 安全头
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
}
```

#### 3. 部署脚本
```bash
#!/bin/bash
# deploy.sh

echo "开始部署智影在线服务前端..."

# 构建项目
npm run build

# 备份旧版本
sudo cp -r /var/www/zyonline-frontend /var/www/zyonline-frontend.backup.$(date +%Y%m%d_%H%M%S)

# 部署新版本
sudo rm -rf /var/www/zyonline-frontend/dist
sudo cp -r dist /var/www/zyonline-frontend/

# 重启 Nginx
sudo systemctl reload nginx

echo "部署完成！"
```

### 方案二：Docker 容器化部署

#### 1. Dockerfile
```dockerfile
# 构建阶段
FROM node:16-alpine as build-stage
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build

# 生产阶段
FROM nginx:alpine as production-stage
COPY --from=build-stage /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

#### 2. nginx.conf
```nginx
events {
    worker_connections 1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;
    
    sendfile        on;
    keepalive_timeout  65;
    
    server {
        listen       80;
        server_name  localhost;
        root   /usr/share/nginx/html;
        index  index.html;

        location /mobile {
            try_files $uri $uri/ /mobile.html;
        }

        location /admin {
            try_files $uri $uri/ /admin.html;
        }

        location /api {
            proxy_pass http://backend:5001;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }
    }
}
```

#### 3. docker-compose.yml
```yaml
version: '3.8'

services:
  frontend:
    build: .
    ports:
      - "80:80"
    depends_on:
      - backend
    networks:
      - zyonline-network

  backend:
    image: zyonline-api:latest
    ports:
      - "5001:80"
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
    networks:
      - zyonline-network

networks:
  zyonline-network:
    driver: bridge
```

#### 4. 部署命令
```bash
# 构建镜像
docker build -t zyonline-frontend .

# 启动服务
docker-compose up -d

# 查看日志
docker-compose logs -f frontend
```

### 方案三：CDN + 对象存储部署

#### 1. 构建优化
```javascript
// vite.config.js
export default defineConfig({
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['vue', 'vue-router', 'pinia'],
          ui: ['element-plus', 'vant']
        }
      }
    }
  }
})
```

#### 2. 上传到 OSS
```bash
# 使用阿里云 OSS CLI
ossutil cp -r dist/ oss://your-bucket/frontend/ --update
```

#### 3. CDN 配置
- 设置缓存规则
- 配置 GZIP 压缩
- 设置 HTTPS 证书

## 🔧 环境配置

### 开发环境
```bash
# .env.development
VITE_API_BASE_URL=http://localhost:5001/api
VITE_APP_TITLE=智影在线服务（开发）
```

### 测试环境
```bash
# .env.test
VITE_API_BASE_URL=https://test-api.zyonline.com/api
VITE_APP_TITLE=智影在线服务（测试）
```

### 生产环境
```bash
# .env.production
VITE_API_BASE_URL=https://api.zyonline.com/api
VITE_APP_TITLE=智影在线服务
```

## 📊 性能优化

### 1. 代码分割
```javascript
// 路由懒加载
const routes = [
  {
    path: '/dashboard',
    component: () => import('@/admin/views/Dashboard.vue')
  }
]
```

### 2. 资源压缩
```javascript
// vite.config.js
import { defineConfig } from 'vite'
import { resolve } from 'path'

export default defineConfig({
  build: {
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true
      }
    }
  }
})
```

### 3. 图片优化
```bash
# 使用 WebP 格式
# 启用图片懒加载
# 使用 CDN 加速
```

## 🔍 监控和日志

### 1. 错误监控
```javascript
// main.js
app.config.errorHandler = (err, vm, info) => {
  console.error('Vue Error:', err, info)
  // 发送到错误监控服务
}
```

### 2. 性能监控
```javascript
// 页面加载时间
window.addEventListener('load', () => {
  const loadTime = performance.now()
  console.log('Page Load Time:', loadTime)
})
```

### 3. 用户行为分析
```javascript
// 集成 Google Analytics 或其他分析工具
```

## 🛡️ 安全配置

### 1. CSP 头设置
```nginx
add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'";
```

### 2. HTTPS 配置
```nginx
server {
    listen 443 ssl http2;
    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;
    
    # SSL 安全配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
}
```

## 🔄 CI/CD 流水线

### GitHub Actions 示例
```yaml
name: Deploy Frontend

on:
  push:
    branches: [ main ]

jobs:
  deploy:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v2
    
    - name: Setup Node.js
      uses: actions/setup-node@v2
      with:
        node-version: '16'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Build
      run: npm run build
      
    - name: Deploy to server
      run: |
        # 部署脚本
```

## 📋 部署检查清单

- [ ] 环境变量配置正确
- [ ] API 接口地址配置正确
- [ ] 静态资源路径配置正确
- [ ] 路由配置正确
- [ ] HTTPS 证书配置
- [ ] 缓存策略配置
- [ ] 错误页面配置
- [ ] 监控和日志配置
- [ ] 备份策略配置
- [ ] 性能测试通过

## 🆘 故障排除

### 常见问题

1. **页面空白**
   - 检查控制台错误
   - 确认静态资源路径
   - 检查路由配置

2. **API 请求失败**
   - 检查代理配置
   - 确认后端服务状态
   - 检查跨域配置

3. **构建失败**
   - 检查 Node.js 版本
   - 清理 node_modules
   - 检查依赖版本冲突

### 调试命令
```bash
# 查看构建详情
npm run build -- --debug

# 分析包大小
npm run build -- --analyze

# 检查依赖
npm audit
```

---

**部署文档版本**: v1.0.0  
**最后更新**: 2025年5月26日
