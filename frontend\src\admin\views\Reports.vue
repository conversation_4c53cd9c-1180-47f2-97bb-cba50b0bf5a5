<template>
  <div class="reports">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>报告管理</span>
          <el-button type="primary" @click="syncReports" :loading="syncing">
            <el-icon><Refresh /></el-icon>
            同步报告
          </el-button>
        </div>
      </template>

      <!-- 筛选条件 -->
      <div class="filter-section">
        <el-form :inline="true" :model="filterForm" class="filter-form">
          <el-form-item label="患者姓名">
            <el-input
              v-model="filterForm.patientName"
              placeholder="请输入患者姓名"
              clearable
            />
          </el-form-item>
          <el-form-item label="报告日期">
            <el-date-picker
              v-model="filterForm.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="loadReports">查询</el-button>
            <el-button @click="resetFilter">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 数据表格 -->
      <el-table
        v-loading="loading"
        :data="reports"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="reportId" label="报告编号" width="150" />
        <el-table-column prop="patientName" label="患者姓名" width="120" />
        <el-table-column prop="patientIdCard" label="身份证号" width="180">
          <template #default="{ row }">
            {{ maskIdCard(row.patientIdCard) }}
          </template>
        </el-table-column>
        <el-table-column prop="title" label="报告标题" min-width="200" show-overflow-tooltip />
        <el-table-column prop="reportDate" label="报告日期" width="120">
          <template #default="{ row }">
            {{ formatDate(row.reportDate) }}
          </template>
        </el-table-column>
        <el-table-column prop="syncTime" label="同步时间" width="160">
          <template #default="{ row }">
            {{ formatDateTime(row.syncTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="220" fixed="right" align="center">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-button
                type="primary"
                size="small"
                @click="viewReport(row)"
                :disabled="!row.reportUrl"
                class="action-btn"
              >
                <el-icon><View /></el-icon>
                查看报告
              </el-button>
              <el-button
                type="danger"
                size="small"
                @click="deleteReport(row)"
                class="action-btn"
              >
                <el-icon><Delete /></el-icon>
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          :prev-text="'上一页'"
          :next-text="'下一页'"
          :page-size-text="'条/页'"
          :total-text="'共 {total} 条'"
          :jumper-text="'前往'"
          @size-change="loadReports"
          @current-change="loadReports"
        />
      </div>
    </el-card>

    <!-- 报告查看对话框 -->
    <el-dialog
      v-model="showReportDialog"
      title="查看报告"
      width="80%"
      :close-on-click-modal="false"
    >
      <div class="report-viewer">
        <div class="report-info">
          <h3>{{ currentReport?.title }}</h3>
          <p><strong>患者:</strong> {{ currentReport?.patientName }}</p>
          <p><strong>报告日期:</strong> {{ formatDate(currentReport?.reportDate) }}</p>
          <p><strong>报告编号:</strong> {{ currentReport?.reportId }}</p>
        </div>

        <div class="report-content">
          <iframe
            v-if="currentReport?.reportUrl"
            :src="currentReport.reportUrl"
            width="100%"
            height="600px"
            frameborder="0"
          ></iframe>
          <div v-else class="no-report">
            <el-empty description="暂无报告内容" />
          </div>
        </div>
      </div>

      <template #footer>
        <el-button @click="showReportDialog = false">关闭</el-button>
        <el-button
          type="primary"
          @click="downloadReport"
          :disabled="!currentReport?.reportUrl"
        >
          下载报告
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Refresh, View, Delete } from '@element-plus/icons-vue'
import dayjs from 'dayjs'

const loading = ref(false)
const syncing = ref(false)
const reports = ref([])
const showReportDialog = ref(false)
const currentReport = ref(null)

const filterForm = reactive({
  patientName: '',
  dateRange: null
})

const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

const formatDate = (date) => {
  return dayjs(date).format('YYYY-MM-DD')
}

const formatDateTime = (dateTime) => {
  return dayjs(dateTime).format('YYYY-MM-DD HH:mm')
}

const maskIdCard = (idCard) => {
  if (!idCard) return ''
  return idCard.replace(/(\d{6})\d{8}(\d{4})/, '$1********$2')
}

const loadReports = async () => {
  loading.value = true
  try {
    // 模拟API调用
    const mockData = [
      {
        id: 1,
        reportId: 'RPT20250526001',
        patientName: '张三',
        patientIdCard: '110101199001011234',
        title: 'PET-CT检查报告',
        reportDate: '2025-05-25',
        reportUrl: 'https://example.com/report1.pdf',
        syncTime: '2025-05-26 10:30:00'
      },
      {
        id: 2,
        reportId: 'RPT20250526002',
        patientName: '李四',
        patientIdCard: '110101199002021234',
        title: 'DR胸部检查报告',
        reportDate: '2025-05-25',
        reportUrl: 'https://example.com/report2.pdf',
        syncTime: '2025-05-26 11:15:00'
      },
      {
        id: 3,
        reportId: 'RPT20250526003',
        patientName: '王五',
        patientIdCard: '110101199003031234',
        title: 'CT腹部检查报告',
        reportDate: '2025-05-24',
        reportUrl: null,
        syncTime: '2025-05-26 09:45:00'
      }
    ]

    reports.value = mockData
    pagination.total = mockData.length
  } catch (error) {
    console.error('获取报告失败:', error)
    ElMessage.error('获取报告失败')
  } finally {
    loading.value = false
  }
}

const resetFilter = () => {
  filterForm.patientName = ''
  filterForm.dateRange = null
  loadReports()
}

const syncReports = async () => {
  syncing.value = true
  try {
    // 模拟同步API调用
    await new Promise(resolve => setTimeout(resolve, 2000))

    ElMessage.success('报告同步成功')
    loadReports()
  } catch (error) {
    console.error('同步报告失败:', error)
    ElMessage.error('同步报告失败')
  } finally {
    syncing.value = false
  }
}

const viewReport = (report) => {
  currentReport.value = report
  showReportDialog.value = true
}

const downloadReport = () => {
  if (currentReport.value?.reportUrl) {
    window.open(currentReport.value.reportUrl, '_blank')
  }
}

const deleteReport = async (report) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除报告"${report.title}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 模拟删除API调用
    await new Promise(resolve => setTimeout(resolve, 500))

    ElMessage.success('删除成功')
    loadReports()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除报告失败:', error)
      ElMessage.error('删除报告失败')
    }
  }
}

onMounted(() => {
  loadReports()
})
</script>

<style scoped>
.reports {
  height: 100%;
  padding: 0;
  width: 100%;
}

.reports :deep(.el-card) {
  height: 100%;
  border-radius: 16px;
  border: none;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.reports :deep(.el-card__body) {
  padding: 25px;
  height: calc(100% - 80px);
  display: flex;
  flex-direction: column;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 15px;
  margin-bottom: 25px;
}

.card-header span {
  font-size: 20px;
  font-weight: 600;
  background: linear-gradient(135deg, #2c3e50, #3498db);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.card-header .el-button {
  border-radius: 12px;
  font-weight: 500;
  padding: 10px 20px;
  transition: all 0.3s ease;
}

.card-header .el-button--primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.card-header .el-button--primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.filter-section {
  margin-bottom: 25px;
  padding: 20px;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.filter-form {
  margin: 0;
}

.filter-form :deep(.el-form-item) {
  margin-bottom: 0;
  margin-right: 20px;
}

.filter-form :deep(.el-form-item__label) {
  font-weight: 500;
  color: #475569;
}

.filter-form :deep(.el-input__wrapper) {
  border-radius: 10px;
  border: 1px solid #d1d5db;
  transition: all 0.3s ease;
}

.filter-form :deep(.el-input__wrapper:hover) {
  border-color: #667eea;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

.filter-form :deep(.el-button) {
  border-radius: 10px;
  font-weight: 500;
  padding: 8px 16px;
  transition: all 0.3s ease;
}

.filter-form :deep(.el-button--primary) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.filter-form :deep(.el-button--primary:hover) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.filter-form :deep(.el-button--default) {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  color: #64748b;
}

.filter-form :deep(.el-button--default:hover) {
  background: #e2e8f0;
  border-color: #cbd5e0;
  transform: translateY(-1px);
}

/* 表格样式 */
.reports :deep(.el-table) {
  width: 100% !important;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  border: none;
  flex: 1;
}

.reports :deep(.el-table__header) {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.reports :deep(.el-table th) {
  background: transparent !important;
  color: #475569;
  font-weight: 600;
  border-bottom: 2px solid #e2e8f0;
  padding: 15px 12px;
  font-size: 14px;
}

.reports :deep(.el-table td) {
  border-bottom: 1px solid #f1f5f9;
  padding: 15px 12px;
  font-size: 14px;
}

.reports :deep(.el-table__row) {
  transition: all 0.3s ease;
}

.reports :deep(.el-table__row:hover) {
  background: #f8fafc !important;
}

/* 操作按钮样式 */
.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
  flex-wrap: wrap;
}

.action-btn {
  border-radius: 8px !important;
  font-weight: 500 !important;
  padding: 6px 12px !important;
  transition: all 0.3s ease !important;
  display: flex;
  align-items: center;
  gap: 4px;
}

.action-btn.el-button--primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  border: none !important;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3) !important;
}

.action-btn.el-button--primary:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4) !important;
}

.action-btn.el-button--danger {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%) !important;
  border: none !important;
  box-shadow: 0 2px 8px rgba(255, 107, 107, 0.3) !important;
}

.action-btn.el-button--danger:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 4px 12px rgba(255, 107, 107, 0.4) !important;
}

.action-btn:disabled {
  opacity: 0.5 !important;
  transform: none !important;
  box-shadow: none !important;
}

.pagination-wrapper {
  margin-top: 25px;
  text-align: right;
  padding: 20px 0 0 0;
}

.pagination-wrapper :deep(.el-pagination) {
  justify-content: flex-end;
}

.pagination-wrapper :deep(.el-pager li) {
  border-radius: 8px;
  margin: 0 4px;
  transition: all 0.3s ease;
  font-weight: 500;
}

.pagination-wrapper :deep(.el-pager li:hover) {
  background: #667eea;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.pagination-wrapper :deep(.el-pager li.is-active) {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.pagination-wrapper :deep(.btn-prev),
.pagination-wrapper :deep(.btn-next) {
  border-radius: 8px;
  transition: all 0.3s ease;
}

.pagination-wrapper :deep(.btn-prev:hover),
.pagination-wrapper :deep(.btn-next:hover) {
  background: #667eea;
  color: white;
  transform: translateY(-2px);
}

/* 对话框样式 */
.reports :deep(.el-dialog) {
  border-radius: 16px;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
}

.reports :deep(.el-dialog__header) {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-bottom: 1px solid #e2e8f0;
  padding: 20px 25px;
  border-radius: 16px 16px 0 0;
}

.reports :deep(.el-dialog__title) {
  font-weight: 600;
  color: #2c3e50;
  font-size: 18px;
}

.reports :deep(.el-dialog__body) {
  padding: 25px;
}

.reports :deep(.el-dialog__footer) {
  padding: 20px 25px;
  border-top: 1px solid #e2e8f0;
  background: #f8fafc;
  border-radius: 0 0 16px 16px;
}

.report-viewer {
  max-height: 80vh;
  overflow-y: auto;
}

.report-info {
  padding: 25px;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-radius: 12px;
  margin-bottom: 20px;
  border: 1px solid #e2e8f0;
}

.report-info h3 {
  margin: 0 0 15px 0;
  color: #2c3e50;
  font-size: 18px;
  font-weight: 600;
}

.report-info p {
  margin: 8px 0;
  color: #64748b;
  font-size: 14px;
}

.report-info strong {
  color: #475569;
  font-weight: 600;
}

.report-content {
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.no-report {
  height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8fafc;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .reports {
    padding: 0;
  }

  .card-header {
    flex-direction: column;
    align-items: stretch;
  }

  .card-header .el-button {
    width: 100%;
    margin-top: 10px;
  }

  .filter-section {
    padding: 15px;
  }

  .filter-form :deep(.el-form-item) {
    margin-right: 0;
    margin-bottom: 15px;
  }

  .action-buttons {
    flex-direction: column;
    gap: 4px;
  }

  .pagination-wrapper {
    text-align: center;
  }
}

/* 加载动画 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.reports {
  animation: fadeInUp 0.6s ease-out;
}
</style>
