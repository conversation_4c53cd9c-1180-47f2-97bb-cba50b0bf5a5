import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import pinia from '@/stores'

// Element Plus
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
// Element Plus中文语言包
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'

// 全局样式
import './styles/global.css'
// 输入框重置样式
import './styles/input-override.css'

const app = createApp(App)

app.use(pinia)
app.use(router)
// 使用Element Plus并配置中文语言包
app.use(ElementPlus, {
  locale: zhCn,
})

app.mount('#app')
