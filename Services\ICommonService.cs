using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using ZyOnlineApi.DTOs;

namespace ZyOnlineApi.Services
{
    public interface ICommonService
    {
        Task<List<ProjectDto>> GetActiveProjectsAsync();
        Task<List<AvailableTimeSlotDto>> GetAvailableTimeSlotsAsync(DateTime date);
        Task<AvailableTimeSlotDto> CreateTimeSlotAsync(AvailableTimeSlotDto timeSlotDto);
        Task<AvailableTimeSlotDto> UpdateTimeSlotAsync(int id, AvailableTimeSlotDto timeSlotDto);
        Task<bool> DeleteTimeSlotAsync(int id);
        Task<List<InstructionDto>> GetActiveInstructionsAsync();
        Task<InstructionDto> GetInstructionByIdAsync(int id);
        Task<InstructionDto> CreateInstructionAsync(InstructionDto instructionDto);
        Task<InstructionDto> UpdateInstructionAsync(int id, InstructionDto instructionDto);
        Task<bool> DeleteInstructionAsync(int id);
    }
}
