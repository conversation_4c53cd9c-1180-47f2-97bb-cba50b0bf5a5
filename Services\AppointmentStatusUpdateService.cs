using System;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace ZyOnlineApi.Services
{
    public class AppointmentStatusUpdateService : BackgroundService
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<AppointmentStatusUpdateService> _logger;

        public AppointmentStatusUpdateService(IServiceProvider serviceProvider, ILogger<AppointmentStatusUpdateService> logger)
        {
            _serviceProvider = serviceProvider;
            _logger = logger;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    using (var scope = _serviceProvider.CreateScope())
                    {
                        var appointmentService = scope.ServiceProvider.GetRequiredService<IAppointmentService>();
                        await appointmentService.UpdateAppointmentStatusesAsync();
                    }

                    _logger.LogInformation("预约状态更新完成: {time}", DateTimeOffset.Now);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "预约状态更新失败: {time}", DateTimeOffset.Now);
                }

                // 每小时执行一次
                await Task.Delay(TimeSpan.FromHours(1), stoppingToken);
            }
        }
    }
}
