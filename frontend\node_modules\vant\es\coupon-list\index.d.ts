export declare const CouponList: import("../utils").WithInstall<import("vue").DefineComponent<import("vue").ExtractPropTypes<{
    code: {
        type: import("vue").PropType<string>;
        default: string;
    };
    coupons: {
        type: import("vue").PropType<import("..").CouponInfo[]>;
        default: () => never[];
    };
    currency: {
        type: import("vue").PropType<string>;
        default: string;
    };
    showCount: {
        type: BooleanConstructor;
        default: true;
    };
    emptyImage: StringConstructor;
    enabledTitle: StringConstructor;
    disabledTitle: StringConstructor;
    disabledCoupons: {
        type: import("vue").PropType<import("..").CouponInfo[]>;
        default: () => never[];
    };
    showExchangeBar: {
        type: BooleanConstructor;
        default: true;
    };
    showCloseButton: {
        type: BooleanConstructor;
        default: true;
    };
    closeButtonText: StringConstructor;
    inputPlaceholder: StringConstructor;
    exchangeMinLength: {
        type: NumberConstructor;
        default: number;
    };
    exchangeButtonText: StringConstructor;
    displayedCouponIndex: {
        type: NumberConstructor;
        default: number;
    };
    exchangeButtonLoading: BooleanConstructor;
    exchangeButtonDisabled: BooleanConstructor;
    chosenCoupon: {
        type: import("vue").PropType<number | number[]>;
        default: number;
    };
}>, () => import("vue/jsx-runtime").JSX.Element, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, ("change" | "exchange" | "update:code")[], "change" | "exchange" | "update:code", import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    code: {
        type: import("vue").PropType<string>;
        default: string;
    };
    coupons: {
        type: import("vue").PropType<import("..").CouponInfo[]>;
        default: () => never[];
    };
    currency: {
        type: import("vue").PropType<string>;
        default: string;
    };
    showCount: {
        type: BooleanConstructor;
        default: true;
    };
    emptyImage: StringConstructor;
    enabledTitle: StringConstructor;
    disabledTitle: StringConstructor;
    disabledCoupons: {
        type: import("vue").PropType<import("..").CouponInfo[]>;
        default: () => never[];
    };
    showExchangeBar: {
        type: BooleanConstructor;
        default: true;
    };
    showCloseButton: {
        type: BooleanConstructor;
        default: true;
    };
    closeButtonText: StringConstructor;
    inputPlaceholder: StringConstructor;
    exchangeMinLength: {
        type: NumberConstructor;
        default: number;
    };
    exchangeButtonText: StringConstructor;
    displayedCouponIndex: {
        type: NumberConstructor;
        default: number;
    };
    exchangeButtonLoading: BooleanConstructor;
    exchangeButtonDisabled: BooleanConstructor;
    chosenCoupon: {
        type: import("vue").PropType<number | number[]>;
        default: number;
    };
}>> & Readonly<{
    onChange?: ((...args: any[]) => any) | undefined;
    onExchange?: ((...args: any[]) => any) | undefined;
    "onUpdate:code"?: ((...args: any[]) => any) | undefined;
}>, {
    code: string;
    currency: string;
    coupons: import("..").CouponInfo[];
    chosenCoupon: number | number[];
    showCount: boolean;
    disabledCoupons: import("..").CouponInfo[];
    showExchangeBar: boolean;
    showCloseButton: boolean;
    exchangeMinLength: number;
    displayedCouponIndex: number;
    exchangeButtonLoading: boolean;
    exchangeButtonDisabled: boolean;
}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>>;
export default CouponList;
export { couponListProps } from './CouponList';
export type { CouponListProps } from './CouponList';
export type { CouponListThemeVars } from './types';
declare module 'vue' {
    interface GlobalComponents {
        VanCouponList: typeof CouponList;
    }
}
