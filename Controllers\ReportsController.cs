using System;
using System.Collections.Generic;
using System.Security.Claims;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ZyOnlineApi.DTOs;
using ZyOnlineApi.Services;

namespace ZyOnlineApi.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize(Roles = "wechat_user")]
    public class ReportsController : ControllerBase
    {
        private readonly IReportService _reportService;

        public ReportsController(IReportService reportService)
        {
            _reportService = reportService;
        }

        /// <summary>
        /// 获取当前用户的所有报告
        /// </summary>
        [HttpGet]
        public async Task<ActionResult<ApiResponse<List<ReportDto>>>> GetReports()
        {
            try
            {
                var wechatOpenId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                var reports = await _reportService.GetReportsByWechatOpenIdAsync(wechatOpenId);
                return Ok(ApiResponse<List<ReportDto>>.SuccessResult(reports));
            }
            catch (Exception ex)
            {
                return BadRequest(ApiResponse<List<ReportDto>>.ErrorResult($"获取报告列表失败: {ex.Message}"));
            }
        }

        /// <summary>
        /// 根据就诊人ID获取报告
        /// </summary>
        [HttpGet("patient/{patientId}")]
        public async Task<ActionResult<ApiResponse<List<ReportDto>>>> GetReportsByPatientId(int patientId)
        {
            try
            {
                var wechatOpenId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                var reports = await _reportService.GetReportsByPatientIdAsync(patientId, wechatOpenId);
                return Ok(ApiResponse<List<ReportDto>>.SuccessResult(reports));
            }
            catch (UnauthorizedAccessException ex)
            {
                return Forbid();
            }
            catch (Exception ex)
            {
                return BadRequest(ApiResponse<List<ReportDto>>.ErrorResult($"获取报告列表失败: {ex.Message}"));
            }
        }

        /// <summary>
        /// 获取报告详情
        /// </summary>
        [HttpGet("{id}")]
        public async Task<ActionResult<ApiResponse<ReportDto>>> GetReport(int id)
        {
            try
            {
                var wechatOpenId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                var report = await _reportService.GetReportByIdAsync(id, wechatOpenId);
                
                if (report == null)
                {
                    return NotFound(ApiResponse<ReportDto>.ErrorResult("报告不存在", 404));
                }

                return Ok(ApiResponse<ReportDto>.SuccessResult(report));
            }
            catch (Exception ex)
            {
                return BadRequest(ApiResponse<ReportDto>.ErrorResult($"获取报告详情失败: {ex.Message}"));
            }
        }

        /// <summary>
        /// 同步联影报告（后台管理端调用）
        /// </summary>
        [HttpPost("sync")]
        [Authorize(Roles = "admin,staff")]
        public async Task<ActionResult<ApiResponse<bool>>> SyncReports()
        {
            try
            {
                await _reportService.SyncReportsFromLianYingAsync();
                return Ok(ApiResponse<bool>.SuccessResult(true, "报告同步成功"));
            }
            catch (Exception ex)
            {
                return BadRequest(ApiResponse<bool>.ErrorResult($"报告同步失败: {ex.Message}"));
            }
        }
    }
}
