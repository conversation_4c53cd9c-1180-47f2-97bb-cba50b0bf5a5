# 智影在线服务前端系统

基于 Vue 3 + Vite 开发的智影在线服务前端系统，包含患者端（移动端）和后台管理端。

## 🚀 技术栈

### 核心框架
- **Vue 3** - 渐进式 JavaScript 框架
- **Vite** - 下一代前端构建工具
- **Vue Router 4** - 官方路由管理器
- **Pinia** - 状态管理库

### UI 组件库
- **Vant 4** - 移动端 UI 组件库（患者端）
- **Element Plus** - 桌面端 UI 组件库（后台管理端）

### 工具库
- **Axios** - HTTP 客户端
- **Day.js** - 日期处理库
- **js-cookie** - Cookie 操作库
- **nprogress** - 进度条库

## 📱 项目结构

```
frontend/
├── src/
│   ├── admin/              # 后台管理端
│   │   ├── views/          # 页面组件
│   │   ├── layout/         # 布局组件
│   │   ├── router/         # 路由配置
│   │   ├── App.vue         # 根组件
│   │   └── main.js         # 入口文件
│   ├── mobile/             # 患者端（移动端）
│   │   ├── views/          # 页面组件
│   │   ├── router/         # 路由配置
│   │   ├── App.vue         # 根组件
│   │   └── main.js         # 入口文件
│   ├── api/                # API 接口
│   ├── stores/             # 状态管理
│   └── utils/              # 工具函数
├── admin.html              # 后台管理端入口
├── mobile.html             # 患者端入口
├── package.json
└── vite.config.js
```

## 🛠️ 开发环境搭建

### 环境要求
- Node.js 16+
- npm 或 yarn

### 安装依赖
```bash
npm install
# 或
yarn install
```

### 启动开发服务器
```bash
npm run dev
# 或
yarn dev
```

### 访问地址
- 患者端（移动端）: http://localhost:3000/mobile.html
- 后台管理端: http://localhost:3000/admin.html

## 📦 构建部署

### 构建生产版本
```bash
npm run build
# 或
yarn build
```

### 预览构建结果
```bash
npm run preview
# 或
yarn preview
```

## 🎯 功能模块

### 患者端（移动端）
- **用户登录** - 微信 OpenID 登录
- **就诊人管理** - 添加、编辑、删除就诊人信息
- **在线预约** - 选择就诊人、项目、时间进行预约
- **我的预约** - 查看、取消预约记录
- **我的报告** - 查看检查报告
- **就诊须知** - 查看就诊相关说明

### 后台管理端
- **管理员登录** - 用户名密码登录
- **仪表板** - 数据统计和概览
- **预约管理** - 查看、筛选、更新预约状态
- **时间段管理** - 管理可预约时间段
- **就诊须知管理** - 编辑就诊须知内容
- **报告管理** - 同步和管理检查报告

## 🔧 配置说明

### API 代理配置
在 `vite.config.js` 中配置了 API 代理：
```javascript
server: {
  proxy: {
    '/api': {
      target: 'http://localhost:5001',
      changeOrigin: true,
      secure: false
    }
  }
}
```

### 环境变量
可以创建 `.env` 文件配置环境变量：
```
VITE_API_BASE_URL=http://localhost:5001/api
```

## 📱 移动端适配

### 视口配置
```html
<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
```

### 响应式设计
- 使用 Vant 组件库确保移动端体验
- 采用 rem 单位和弹性布局
- 支持触摸手势和移动端交互

## 🔐 权限控制

### 路由守卫
```javascript
router.beforeEach((to, from, next) => {
  const userStore = useUserStore()
  
  if (to.meta.requiresAuth && !userStore.token) {
    next('/login')
  } else {
    next()
  }
})
```

### 角色权限
- **wechat_user** - 微信用户（患者端）
- **admin** - 系统管理员
- **staff** - 工作人员

## 🎨 主题定制

### Vant 主题定制
```scss
:root {
  --van-primary-color: #1989fa;
  --van-success-color: #07c160;
  --van-warning-color: #ff976a;
  --van-danger-color: #ee0a24;
}
```

### Element Plus 主题定制
```scss
:root {
  --el-color-primary: #409eff;
  --el-color-success: #67c23a;
  --el-color-warning: #e6a23c;
  --el-color-danger: #f56c6c;
}
```

## 🚀 部署指南

### Nginx 配置示例
```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /path/to/dist;
    index index.html;

    # 患者端
    location /mobile {
        try_files $uri $uri/ /mobile.html;
    }

    # 后台管理端
    location /admin {
        try_files $uri $uri/ /admin.html;
    }

    # API 代理
    location /api {
        proxy_pass http://localhost:5001;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

### Docker 部署
```dockerfile
FROM nginx:alpine
COPY dist/ /usr/share/nginx/html/
COPY nginx.conf /etc/nginx/nginx.conf
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

## 🔍 开发调试

### 开发者工具
- Vue DevTools - Vue 组件调试
- Network 面板 - API 请求调试
- Console 面板 - 错误日志查看

### 常用调试技巧
```javascript
// API 请求调试
console.log('API Request:', config)
console.log('API Response:', response)

// 状态管理调试
const userStore = useUserStore()
console.log('User State:', userStore.$state)
```

## 📝 代码规范

### ESLint 配置
项目使用 ESLint 进行代码规范检查：
```bash
npm run lint
```

### 提交规范
建议使用 Conventional Commits 规范：
- `feat:` 新功能
- `fix:` 修复问题
- `docs:` 文档更新
- `style:` 代码格式调整
- `refactor:` 代码重构

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 联系方式

如有问题或建议，请联系开发团队。

---

**最后更新**: 2025年5月26日  
**版本**: v1.0.0
