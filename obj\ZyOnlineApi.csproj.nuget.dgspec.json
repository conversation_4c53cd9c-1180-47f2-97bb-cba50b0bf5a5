{"format": 1, "restore": {"D:\\工作\\丰选\\丰选Git\\zyonline_api_test\\ZyOnlineApi.csproj": {}}, "projects": {"D:\\工作\\丰选\\丰选Git\\zyonline_api_test\\ZyOnlineApi.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\工作\\丰选\\丰选Git\\zyonline_api_test\\ZyOnlineApi.csproj", "projectName": "ZyOnlineApi", "projectPath": "D:\\工作\\丰选\\丰选Git\\zyonline_api_test\\ZyOnlineApi.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\工作\\丰选\\丰选Git\\zyonline_api_test\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netcoreapp3.1"], "sources": {"https://api.nuget.org/v3/index.json": {}, "https://nuget.org/api/v2/": {}}, "frameworks": {"netcoreapp3.1": {"targetAlias": "netcoreapp3.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netcoreapp3.1": {"targetAlias": "netcoreapp3.1", "dependencies": {"AutoMapper": {"target": "Package", "version": "[10.1.1, )"}, "AutoMapper.Extensions.Microsoft.DependencyInjection": {"target": "Package", "version": "[8.1.1, )"}, "BCrypt.Net-Next": {"target": "Package", "version": "[4.0.3, )"}, "Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[3.1.32, )"}, "Microsoft.AspNetCore.Cors": {"target": "Package", "version": "[2.2.0, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[3.1.32, )"}, "Microsoft.EntityFrameworkCore.Design": {"target": "Package", "version": "[3.1.32, )"}, "Pomelo.EntityFrameworkCore.MySql": {"target": "Package", "version": "[3.2.7, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[5.6.3, )"}, "System.IdentityModel.Tokens.Jwt": {"target": "Package", "version": "[6.10.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.403\\RuntimeIdentifierGraph.json"}}}}}