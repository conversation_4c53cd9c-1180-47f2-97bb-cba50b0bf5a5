<template>
  <div class="instructions">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>就诊须知管理</span>
          <el-button type="primary" @click="showAddDialog = true">
            <el-icon><Plus /></el-icon>
            添加须知
          </el-button>
        </div>
      </template>

      <!-- 数据表格 -->
      <el-table
        v-loading="loading"
        :data="instructions"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="title" label="标题" width="200" />
        <el-table-column prop="content" label="内容" show-overflow-tooltip />
        <el-table-column prop="isActive" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.isActive ? 'success' : 'danger'">
              {{ row.isActive ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createdAt" label="创建时间" width="160">
          <template #default="{ row }">
            {{ formatDateTime(row.createdAt) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="editInstruction(row)">
              编辑
            </el-button>
            <el-button
              :type="row.isActive ? 'danger' : 'success'"
              size="small"
              @click="toggleStatus(row)"
            >
              {{ row.isActive ? '禁用' : '启用' }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 添加/编辑对话框 -->
    <el-dialog
      v-model="showAddDialog"
      :title="editingInstruction ? '编辑就诊须知' : '添加就诊须知'"
      width="600px"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="80px"
      >
        <el-form-item label="标题" prop="title">
          <el-input v-model="form.title" placeholder="请输入标题" />
        </el-form-item>
        
        <el-form-item label="内容" prop="content">
          <el-input
            v-model="form.content"
            type="textarea"
            :rows="8"
            placeholder="请输入内容"
          />
        </el-form-item>
        
        <el-form-item label="状态" prop="isActive">
          <el-switch v-model="form.isActive" />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showAddDialog = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          确定
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { getInstructions, createInstruction, updateInstruction, deleteInstruction } from '@/api/common'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import dayjs from 'dayjs'

const loading = ref(false)
const submitting = ref(false)
const instructions = ref([])
const showAddDialog = ref(false)
const editingInstruction = ref(null)

const form = reactive({
  title: '',
  content: '',
  isActive: true
})

const rules = {
  title: [
    { required: true, message: '请输入标题', trigger: 'blur' }
  ],
  content: [
    { required: true, message: '请输入内容', trigger: 'blur' }
  ]
}

const formatDateTime = (dateTime) => {
  return dayjs(dateTime).format('YYYY-MM-DD HH:mm')
}

const loadInstructions = async () => {
  loading.value = true
  try {
    instructions.value = await getInstructions()
  } catch (error) {
    console.error('获取就诊须知失败:', error)
  } finally {
    loading.value = false
  }
}

const editInstruction = (instruction) => {
  editingInstruction.value = instruction
  Object.assign(form, instruction)
  showAddDialog.value = true
}

const handleSubmit = async () => {
  submitting.value = true
  try {
    if (editingInstruction.value) {
      await updateInstruction(editingInstruction.value.id, form)
      ElMessage.success('更新成功')
    } else {
      await createInstruction(form)
      ElMessage.success('添加成功')
    }
    showAddDialog.value = false
    resetForm()
    loadInstructions()
  } catch (error) {
    console.error('操作失败:', error)
  } finally {
    submitting.value = false
  }
}

const toggleStatus = async (instruction) => {
  try {
    const newStatus = !instruction.isActive
    await updateInstruction(instruction.id, {
      ...instruction,
      isActive: newStatus
    })
    ElMessage.success(newStatus ? '启用成功' : '禁用成功')
    loadInstructions()
  } catch (error) {
    console.error('状态更新失败:', error)
  }
}

const resetForm = () => {
  Object.assign(form, {
    title: '',
    content: '',
    isActive: true
  })
  editingInstruction.value = null
}

onMounted(() => {
  loadInstructions()
})
</script>

<style scoped>
.instructions {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
