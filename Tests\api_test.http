### 智影在线服务 API 测试文件

@baseUrl = https://localhost:5001/api
@adminToken = 
@wechatToken = 

### 1. 健康检查
GET {{baseUrl}}/health

### 2. 数据库连接检查
GET {{baseUrl}}/health/database

### 3. 管理员登录
POST {{baseUrl}}/auth/login
Content-Type: application/json

{
  "username": "admin",
  "password": "admin123"
}

### 4. 微信用户登录
POST {{baseUrl}}/auth/wechat-login
Content-Type: application/json

{
  "openId": "test_openid_123456"
}

### 5. 获取当前用户信息
GET {{baseUrl}}/auth/me
Authorization: Bearer {{adminToken}}

### 6. 获取检查项目列表
GET {{baseUrl}}/common/projects
Authorization: Bearer {{wechatToken}}

### 7. 获取就诊须知
GET {{baseUrl}}/common/instructions

### 8. 获取可预约时间段
GET {{baseUrl}}/common/time-slots?date=2024-01-15
Authorization: Bearer {{wechatToken}}

### 9. 创建就诊人
POST {{baseUrl}}/patients
Authorization: Bearer {{wechatToken}}
Content-Type: application/json

{
  "name": "张三",
  "gender": "男",
  "idCard": "110101199001011234",
  "phone": "13800138000"
}

### 10. 获取就诊人列表
GET {{baseUrl}}/patients
Authorization: Bearer {{wechatToken}}

### 11. 更新就诊人信息
PUT {{baseUrl}}/patients/1
Authorization: Bearer {{wechatToken}}
Content-Type: application/json

{
  "name": "张三丰",
  "gender": "男",
  "idCard": "110101199001011234",
  "phone": "13800138001"
}

### 12. 创建预约
POST {{baseUrl}}/appointments
Authorization: Bearer {{wechatToken}}
Content-Type: application/json

{
  "patientId": 1,
  "projectId": 1,
  "appointmentTime": "2024-01-15T09:00:00",
  "remarks": "首次检查"
}

### 13. 获取我的预约
GET {{baseUrl}}/appointments/my
Authorization: Bearer {{wechatToken}}

### 14. 取消预约
PUT {{baseUrl}}/appointments/1/cancel
Authorization: Bearer {{wechatToken}}

### 15. 获取预约列表（后台管理）
GET {{baseUrl}}/appointments?status=全部&pageIndex=1&pageSize=10
Authorization: Bearer {{adminToken}}

### 16. 更新预约状态（后台管理）
PUT {{baseUrl}}/appointments/1/status
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "status": "已完成",
  "remarks": "检查完成"
}

### 17. 获取报告列表
GET {{baseUrl}}/reports
Authorization: Bearer {{wechatToken}}

### 18. 获取指定就诊人的报告
GET {{baseUrl}}/reports/patient/1
Authorization: Bearer {{wechatToken}}

### 19. 同步联影报告（后台管理）
POST {{baseUrl}}/reports/sync
Authorization: Bearer {{adminToken}}

### 20. 创建时间段（后台管理）
POST {{baseUrl}}/common/time-slots
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "date": "2024-01-16",
  "startTime": "08:00:00",
  "endTime": "09:00:00",
  "maxAppointments": 10,
  "currentAppointments": 0,
  "isAvailable": true
}

### 21. 创建就诊须知（后台管理）
POST {{baseUrl}}/common/instructions
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "title": "新的就诊须知",
  "content": "这是新的就诊须知内容",
  "isActive": true
}

### 22. 更新就诊须知（后台管理）
PUT {{baseUrl}}/common/instructions/1
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "title": "更新的就诊须知",
  "content": "这是更新后的就诊须知内容",
  "isActive": true
}
