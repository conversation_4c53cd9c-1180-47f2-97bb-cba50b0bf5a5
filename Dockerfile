# 使用官方的 .NET Core 3.1 运行时作为基础镜像
FROM mcr.microsoft.com/dotnet/aspnet:3.1 AS base
WORKDIR /app
EXPOSE 80
EXPOSE 443

# 使用官方的 .NET Core 3.1 SDK 作为构建镜像
FROM mcr.microsoft.com/dotnet/sdk:3.1 AS build
WORKDIR /src
COPY ["ZyOnlineApi.csproj", "."]
RUN dotnet restore "./ZyOnlineApi.csproj"
COPY . .
WORKDIR "/src/."
RUN dotnet build "ZyOnlineApi.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "ZyOnlineApi.csproj" -c Release -o /app/publish

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "ZyOnlineApi.dll"]
