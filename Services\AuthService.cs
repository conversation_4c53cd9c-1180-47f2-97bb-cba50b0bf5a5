using System;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.IdentityModel.Tokens;
using ZyOnlineApi.Data;
using ZyOnlineApi.DTOs;

namespace ZyOnlineApi.Services
{
    public class AuthService : IAuthService
    {
        private readonly ApplicationDbContext _context;
        private readonly IConfiguration _configuration;

        public AuthService(ApplicationDbContext context, IConfiguration configuration)
        {
            _context = context;
            _configuration = configuration;
        }

        public async Task<LoginResponseDto> LoginAsync(LoginDto loginDto)
        {
            var user = await _context.Users
                .FirstOrDefaultAsync(u => u.Username == loginDto.Username && u.IsActive);

            // 临时使用简单密码验证，用于测试
            if (user == null || user.PasswordHash != loginDto.Password)
            {
                throw new UnauthorizedAccessException("用户名或密码错误");
            }

            var token = GenerateJwtToken(user.Id.ToString(), user.Role, user.Username);
            var expirationMinutes = int.Parse(_configuration["JwtSettings:ExpirationInMinutes"]);

            return new LoginResponseDto
            {
                Token = token,
                Username = user.Username,
                RealName = user.RealName,
                Role = user.Role,
                ExpiresAt = DateTime.Now.AddMinutes(expirationMinutes)
            };
        }

        public async Task<string> GenerateWechatTokenAsync(string openId)
        {
            // 这里可以添加微信用户验证逻辑
            // 目前简单生成一个包含openId的token
            return GenerateJwtToken(openId, "wechat_user", openId);
        }

        public string GenerateJwtToken(string userId, string role, string username)
        {
            var jwtSettings = _configuration.GetSection("JwtSettings");
            var secretKey = jwtSettings["SecretKey"];
            var issuer = jwtSettings["Issuer"];
            var audience = jwtSettings["Audience"];
            var expirationMinutes = int.Parse(jwtSettings["ExpirationInMinutes"]);

            var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(secretKey));
            var credentials = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);

            var claims = new[]
            {
                new Claim(ClaimTypes.NameIdentifier, userId),
                new Claim(ClaimTypes.Name, username),
                new Claim(ClaimTypes.Role, role),
                new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString()),
                new Claim(JwtRegisteredClaimNames.Iat,
                    new DateTimeOffset(DateTime.UtcNow).ToUnixTimeSeconds().ToString(),
                    ClaimValueTypes.Integer64)
            };

            var token = new JwtSecurityToken(
                issuer: issuer,
                audience: audience,
                claims: claims,
                expires: DateTime.Now.AddMinutes(expirationMinutes),
                signingCredentials: credentials
            );

            return new JwtSecurityTokenHandler().WriteToken(token);
        }
    }
}
