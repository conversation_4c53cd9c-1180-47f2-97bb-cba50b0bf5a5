import request from '@/utils/request'

// 获取我的预约列表
export function getMyAppointments() {
  return request({
    url: '/appointments/my',
    method: 'get'
  })
}

// 创建预约
export function createAppointment(data) {
  return request({
    url: '/appointments',
    method: 'post',
    data
  })
}

// 取消预约
export function cancelAppointment(id) {
  return request({
    url: `/appointments/${id}/cancel`,
    method: 'put'
  })
}

// 获取预约详情
export function getAppointment(id) {
  return request({
    url: `/appointments/${id}`,
    method: 'get'
  })
}

// 获取预约列表（后台管理）
export function getAppointments(params) {
  return request({
    url: '/appointments',
    method: 'get',
    params
  })
}

// 更新预约状态（后台管理）
export function updateAppointmentStatus(id, data) {
  return request({
    url: `/appointments/${id}/status`,
    method: 'put',
    data
  })
}

// 获取可预约时间段
export function getAvailableTimeSlots(date) {
  return request({
    url: '/appointments/available-slots',
    method: 'get',
    params: { date }
  })
}
