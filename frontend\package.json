{"name": "zyonline-frontend", "version": "1.0.0", "description": "智影在线服务前端系统", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore"}, "dependencies": {"vue": "^3.3.4", "vue-router": "^4.2.4", "pinia": "^2.1.6", "axios": "^1.5.0", "element-plus": "^2.3.9", "@element-plus/icons-vue": "^2.1.0", "dayjs": "^1.11.9", "js-cookie": "^3.0.5", "nprogress": "^0.2.0", "vant": "^4.6.2"}, "devDependencies": {"@vitejs/plugin-vue": "^4.3.4", "vite": "^4.4.9", "eslint": "^8.47.0", "eslint-plugin-vue": "^9.17.0", "@vue/eslint-config-prettier": "^8.0.0", "prettier": "^3.0.2", "unplugin-auto-import": "^0.16.6", "unplugin-vue-components": "^0.25.2", "sass": "^1.66.1"}}