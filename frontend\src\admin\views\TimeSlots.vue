<template>
  <div class="time-slots">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>时间段管理</span>
          <el-button type="primary" @click="showAddDialog = true">
            <el-icon><Plus /></el-icon>
            添加时间段
          </el-button>
        </div>
      </template>

      <!-- 筛选条件 -->
      <div class="filter-section">
        <el-form :inline="true" :model="filterForm" class="filter-form">
          <el-form-item label="日期">
            <el-date-picker
              v-model="filterForm.date"
              type="date"
              placeholder="选择日期"
              @change="loadTimeSlots"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="loadTimeSlots">查询</el-button>
            <el-button @click="resetFilter">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 数据表格 -->
      <el-table
        v-loading="loading"
        :data="timeSlots"
        stripe
        style="width: 100%"
        :header-cell-style="{ background: '#f8fafc', color: '#475569' }"
      >
        <el-table-column prop="id" label="编号" width="80" align="center" />
        <el-table-column prop="date" label="预约日期" width="140" align="center">
          <template #default="{ row }">
            {{ formatDate(row.date) }}
          </template>
        </el-table-column>
        <el-table-column prop="startTime" label="开始时间" width="120" align="center" />
        <el-table-column prop="endTime" label="结束时间" width="120" align="center" />
        <el-table-column prop="maxAppointments" label="最大预约数" width="140" align="center">
          <template #default="{ row }">
            <span class="number-highlight">{{ row.maxAppointments }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="currentAppointments" label="当前预约数" width="140" align="center">
          <template #default="{ row }">
            <span class="number-highlight current-count">{{ row.currentAppointments }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="isAvailable" label="预约状态" width="120" align="center">
          <template #default="{ row }">
            <el-tag :type="row.isAvailable ? 'success' : 'danger'" size="small">
              {{ row.isAvailable ? '可预约' : '不可预约' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createdAt" label="创建时间" min-width="180" align="center">
          <template #default="{ row }">
            {{ formatDateTime(row.createdAt) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right" align="center">
          <template #default="{ row }">
            <div class="action-buttons" style="display: flex; gap: 8px; justify-content: center; align-items: center;">
              <el-button
                type="primary"
                size="small"
                @click="editTimeSlot(row)"
                class="action-btn"
                style="margin: 0; white-space: nowrap;"
              >
                <el-icon><Edit /></el-icon>
                编辑
              </el-button>
              <el-button
                :type="row.isAvailable ? 'danger' : 'success'"
                size="small"
                @click="toggleAvailability(row)"
                class="action-btn"
                style="margin: 0; white-space: nowrap;"
              >
                <el-icon v-if="row.isAvailable"><Close /></el-icon>
                <el-icon v-else><Check /></el-icon>
                {{ row.isAvailable ? '禁用' : '启用' }}
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          :prev-text="'上一页'"
          :next-text="'下一页'"
          @size-change="loadTimeSlots"
          @current-change="loadTimeSlots"
        />
      </div>
    </el-card>

    <!-- 添加/编辑对话框 -->
    <el-dialog
      v-model="showAddDialog"
      :title="editingTimeSlot ? '编辑时间段' : '添加时间段'"
      width="500px"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="日期" prop="date">
          <el-date-picker
            v-model="form.date"
            type="date"
            placeholder="选择日期"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="开始时间" prop="startTime">
          <el-time-picker
            v-model="form.startTime"
            placeholder="选择开始时间"
            format="HH:mm"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="结束时间" prop="endTime">
          <el-time-picker
            v-model="form.endTime"
            placeholder="选择结束时间"
            format="HH:mm"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="最大预约数" prop="maxAppointments">
          <el-input-number
            v-model="form.maxAppointments"
            :min="1"
            :max="100"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="状态" prop="isAvailable">
          <el-switch v-model="form.isAvailable" />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="showAddDialog = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          确定
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Edit, Close, Check } from '@element-plus/icons-vue'
import dayjs from 'dayjs'

const loading = ref(false)
const submitting = ref(false)
const timeSlots = ref([])
const showAddDialog = ref(false)
const editingTimeSlot = ref(null)

const filterForm = reactive({
  date: null
})

const form = reactive({
  date: null,
  startTime: null,
  endTime: null,
  maxAppointments: 10,
  isAvailable: true
})

const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

const rules = {
  date: [
    { required: true, message: '请选择日期', trigger: 'change' }
  ],
  startTime: [
    { required: true, message: '请选择开始时间', trigger: 'change' }
  ],
  endTime: [
    { required: true, message: '请选择结束时间', trigger: 'change' }
  ],
  maxAppointments: [
    { required: true, message: '请输入最大预约数', trigger: 'blur' }
  ]
}

const formatDate = (date) => {
  return dayjs(date).format('YYYY-MM-DD')
}

const formatDateTime = (dateTime) => {
  return dayjs(dateTime).format('YYYY-MM-DD HH:mm')
}

const loadTimeSlots = async () => {
  loading.value = true
  try {
    // 模拟API调用
    const mockData = [
      {
        id: 1,
        date: '2025-05-26',
        startTime: '08:00',
        endTime: '09:00',
        maxAppointments: 10,
        currentAppointments: 3,
        isAvailable: true,
        createdAt: '2025-05-26 10:00:00'
      },
      {
        id: 2,
        date: '2025-05-26',
        startTime: '09:00',
        endTime: '10:00',
        maxAppointments: 10,
        currentAppointments: 8,
        isAvailable: true,
        createdAt: '2025-05-26 10:00:00'
      }
    ]

    timeSlots.value = mockData
    pagination.total = mockData.length
  } catch (error) {
    console.error('获取时间段失败:', error)
    ElMessage.error('获取时间段失败')
  } finally {
    loading.value = false
  }
}

const resetFilter = () => {
  filterForm.date = null
  loadTimeSlots()
}

const editTimeSlot = (timeSlot) => {
  editingTimeSlot.value = timeSlot
  Object.assign(form, {
    ...timeSlot,
    date: new Date(timeSlot.date),
    startTime: new Date(`2000-01-01 ${timeSlot.startTime}`),
    endTime: new Date(`2000-01-01 ${timeSlot.endTime}`)
  })
  showAddDialog.value = true
}

const handleSubmit = async () => {
  submitting.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    ElMessage.success(editingTimeSlot.value ? '更新成功' : '添加成功')
    showAddDialog.value = false
    resetForm()
    loadTimeSlots()
  } catch (error) {
    console.error('操作失败:', error)
    ElMessage.error('操作失败')
  } finally {
    submitting.value = false
  }
}

const toggleAvailability = async (timeSlot) => {
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))

    timeSlot.isAvailable = !timeSlot.isAvailable
    ElMessage.success(timeSlot.isAvailable ? '启用成功' : '禁用成功')
  } catch (error) {
    console.error('状态更新失败:', error)
    ElMessage.error('状态更新失败')
  }
}

const resetForm = () => {
  Object.assign(form, {
    date: null,
    startTime: null,
    endTime: null,
    maxAppointments: 10,
    isAvailable: true
  })
  editingTimeSlot.value = null
}

onMounted(() => {
  loadTimeSlots()
})
</script>

<style scoped>
.time-slots {
  height: 100%;
  padding: 0;
  width: 100%;
}

.time-slots :deep(.el-card) {
  height: 100%;
  border-radius: 16px;
  border: none;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.time-slots :deep(.el-card__body) {
  padding: 25px;
  height: calc(100% - 80px);
  display: flex;
  flex-direction: column;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 15px;
  margin-bottom: 25px;
}

.card-header span {
  font-size: 20px;
  font-weight: 600;
  background: linear-gradient(135deg, #2c3e50, #3498db);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.card-header .el-button {
  border-radius: 12px;
  font-weight: 500;
  padding: 10px 20px;
  transition: all 0.3s ease;
}

.card-header .el-button--primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.card-header .el-button--primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.filter-section {
  margin-bottom: 25px;
  padding: 20px;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.filter-form {
  margin: 0;
}

.filter-form :deep(.el-form-item) {
  margin-bottom: 0;
  margin-right: 20px;
}

.filter-form :deep(.el-form-item__label) {
  font-weight: 500;
  color: #475569;
}

.filter-form :deep(.el-input__wrapper) {
  border-radius: 10px;
  border: 1px solid #d1d5db;
  transition: all 0.3s ease;
}

.filter-form :deep(.el-input__wrapper:hover) {
  border-color: #667eea;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

.filter-form :deep(.el-button) {
  border-radius: 10px;
  font-weight: 500;
  padding: 8px 16px;
  transition: all 0.3s ease;
}

.filter-form :deep(.el-button--primary) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.filter-form :deep(.el-button--primary:hover) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.filter-form :deep(.el-button--default) {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  color: #64748b;
}

.filter-form :deep(.el-button--default:hover) {
  background: #e2e8f0;
  border-color: #cbd5e0;
  transform: translateY(-1px);
}

/* 表格样式 */
.time-slots :deep(.el-table) {
  width: 100% !important;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  border: none;
  flex: 1;
}

.time-slots :deep(.el-table__header) {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.time-slots :deep(.el-table th) {
  background: transparent !important;
  color: #475569;
  font-weight: 600;
  border-bottom: 2px solid #e2e8f0;
  padding: 15px 12px;
  font-size: 14px;
}

.time-slots :deep(.el-table td) {
  border-bottom: 1px solid #f1f5f9;
  padding: 15px 12px;
  font-size: 14px;
}

.time-slots :deep(.el-table__row) {
  transition: all 0.3s ease;
}

.time-slots :deep(.el-table__row:hover) {
  background: #f8fafc !important;
}

/* 操作按钮样式 */
.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
  align-items: center;
  flex-wrap: nowrap;
}

.action-btn {
  border-radius: 8px !important;
  font-weight: 500 !important;
  padding: 6px 12px !important;
  transition: all 0.3s ease !important;
  display: flex;
  align-items: center;
  gap: 4px;
  white-space: nowrap;
}

.time-slots :deep(.el-button--small) {
  border-radius: 8px;
  font-weight: 500;
  padding: 6px 12px;
  margin: 0;
  transition: all 0.3s ease;
}

.action-btn.el-button--primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  border: none !important;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3) !important;
}

.action-btn.el-button--primary:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4) !important;
}

.action-btn.el-button--success {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%) !important;
  border: none !important;
  box-shadow: 0 2px 8px rgba(67, 233, 123, 0.3) !important;
}

.action-btn.el-button--success:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 4px 12px rgba(67, 233, 123, 0.4) !important;
}

.action-btn.el-button--danger {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%) !important;
  border: none !important;
  box-shadow: 0 2px 8px rgba(255, 107, 107, 0.3) !important;
}

.action-btn.el-button--danger:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 4px 12px rgba(255, 107, 107, 0.4) !important;
}

.time-slots :deep(.el-tag) {
  border-radius: 20px;
  padding: 4px 12px;
  font-weight: 500;
  border: none;
}

.time-slots :deep(.el-tag--success) {
  background: linear-gradient(135deg, #43e97b, #38f9d7);
  color: white;
}

.time-slots :deep(.el-tag--danger) {
  background: linear-gradient(135deg, #ff6b6b, #ee5a52);
  color: white;
}

.pagination-wrapper {
  margin-top: 25px;
  text-align: right;
  padding: 20px 0 0 0;
}

.pagination-wrapper :deep(.el-pagination) {
  justify-content: flex-end;
}

.pagination-wrapper :deep(.el-pager li) {
  border-radius: 8px;
  margin: 0 4px;
  transition: all 0.3s ease;
  font-weight: 500;
}

.pagination-wrapper :deep(.el-pager li:hover) {
  background: #667eea;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.pagination-wrapper :deep(.el-pager li.is-active) {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.pagination-wrapper :deep(.btn-prev),
.pagination-wrapper :deep(.btn-next) {
  border-radius: 8px;
  transition: all 0.3s ease;
}

.pagination-wrapper :deep(.btn-prev:hover),
.pagination-wrapper :deep(.btn-next:hover) {
  background: #667eea;
  color: white;
  transform: translateY(-2px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .action-buttons {
    flex-direction: column;
    gap: 4px;
  }

  .action-btn {
    width: 100%;
    justify-content: center;
  }

  .time-slots :deep(.el-table-column) {
    min-width: auto;
  }
}

@media (max-width: 1200px) {
  .action-buttons {
    gap: 4px;
  }

  .action-btn {
    padding: 4px 8px !important;
    font-size: 12px;
  }
}

/* 对话框样式 */
.time-slots :deep(.el-dialog) {
  border-radius: 16px;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
}

.time-slots :deep(.el-dialog__header) {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-bottom: 1px solid #e2e8f0;
  padding: 20px 25px;
  border-radius: 16px 16px 0 0;
}

.time-slots :deep(.el-dialog__title) {
  font-weight: 600;
  color: #2c3e50;
  font-size: 18px;
}

.time-slots :deep(.el-dialog__body) {
  padding: 25px;
}

.time-slots :deep(.el-dialog__footer) {
  padding: 20px 25px;
  border-top: 1px solid #e2e8f0;
  background: #f8fafc;
  border-radius: 0 0 16px 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .time-slots {
    padding: 0;
  }

  .card-header {
    flex-direction: column;
    align-items: stretch;
  }

  .card-header .el-button {
    width: 100%;
    margin-top: 10px;
  }

  .filter-section {
    padding: 15px;
  }

  .filter-form :deep(.el-form-item) {
    margin-right: 0;
    margin-bottom: 15px;
  }

  .pagination-wrapper {
    text-align: center;
  }
}

/* 加载动画 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.time-slots {
  animation: fadeInUp 0.6s ease-out;
}

/* 数字高亮样式 */
.number-highlight {
  font-weight: 600;
  color: #3498db;
  background: rgba(52, 152, 219, 0.1);
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 14px;
}

.current-count {
  color: #e74c3c;
  background: rgba(231, 76, 60, 0.1);
}

/* 表格行样式增强 */
.time-slots :deep(.el-table__row) {
  cursor: pointer;
}

.time-slots :deep(.el-table__row:nth-child(even)) {
  background: rgba(248, 250, 252, 0.5);
}

.time-slots :deep(.el-table__row:hover) {
  background: #f8fafc !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 按钮组样式 */
.time-slots :deep(.el-button-group) {
  display: flex;
  gap: 8px;
}

.time-slots :deep(.el-button .el-icon) {
  margin-right: 4px;
}
</style>
