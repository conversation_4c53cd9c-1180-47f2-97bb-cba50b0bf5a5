<template>
  <div class="add-patient">
    <van-nav-bar title="添加就诊人" left-arrow @click-left="$router.back()" />
    
    <div class="content">
      <van-form @submit="handleSubmit">
        <van-cell-group inset>
          <van-field
            v-model="form.name"
            name="name"
            label="姓名"
            placeholder="请输入真实姓名"
            :rules="nameRules"
          />
          
          <van-field name="gender" label="性别">
            <template #input>
              <van-radio-group v-model="form.gender" direction="horizontal">
                <van-radio name="男">男</van-radio>
                <van-radio name="女">女</van-radio>
              </van-radio-group>
            </template>
          </van-field>
          
          <van-field
            v-model="form.idCard"
            name="idCard"
            label="身份证号"
            placeholder="请输入18位身份证号"
            :rules="idCardRules"
          />
          
          <van-field
            v-model="form.phone"
            name="phone"
            label="手机号"
            placeholder="请输入11位手机号"
            :rules="phoneRules"
          />
        </van-cell-group>
        
        <div class="form-tips">
          <van-notice-bar
            left-icon="info-o"
            text="请确保信息准确无误，身份证号将用于预约验证"
          />
        </div>
        
        <div class="submit-button">
          <van-button
            round
            block
            type="primary"
            native-type="submit"
            :loading="loading"
          >
            保存
          </van-button>
        </div>
      </van-form>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { createPatient } from '@/api/patient'
import { showToast } from 'vant'

const router = useRouter()

const loading = ref(false)
const form = ref({
  name: '',
  gender: '男',
  idCard: '',
  phone: ''
})

// 验证规则
const nameRules = [
  { required: true, message: '请输入姓名' },
  { pattern: /^[\u4e00-\u9fa5]{2,10}$/, message: '请输入2-10位中文姓名' }
]

const idCardRules = [
  { required: true, message: '请输入身份证号' },
  { 
    pattern: /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/, 
    message: '请输入正确的身份证号' 
  }
]

const phoneRules = [
  { required: true, message: '请输入手机号' },
  { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号' }
]

const handleSubmit = async () => {
  loading.value = true
  try {
    await createPatient(form.value)
    showToast.success('添加成功')
    router.back()
  } catch (error) {
    console.error('添加失败:', error)
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.add-patient {
  background-color: #f7f8fa;
  min-height: 100vh;
}

.content {
  padding: 16px;
}

.form-tips {
  margin: 16px 0;
}

.submit-button {
  margin-top: 32px;
}
</style>
