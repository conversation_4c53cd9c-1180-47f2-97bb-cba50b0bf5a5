import { createRouter, createWebHashHistory } from 'vue-router'
import { useUserStore } from '@/stores/user'

const routes = [
  {
    path: '/',
    redirect: '/dashboard'
  },
  {
    path: '/login',
    name: 'AdminLogin',
    component: () => import('@/admin/views/Login.vue'),
    meta: { title: '管理员登录' }
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: () => import('@/admin/layout/Layout.vue'),
    redirect: '/dashboard/home',
    meta: { title: '控制台', requiresAuth: true },
    children: [
      {
        path: 'home',
        name: 'DashboardHome',
        component: () => import('@/admin/views/Dashboard.vue'),
        meta: { title: '首页', icon: 'house' }
      },
      {
        path: 'appointments',
        name: 'Appointments',
        component: () => import('@/admin/views/Appointments.vue'),
        meta: { title: '预约管理', icon: 'calendar' }
      },
      {
        path: 'time-slots',
        name: 'TimeSlots',
        component: () => import('@/admin/views/TimeSlots.vue'),
        meta: { title: '时间段管理', icon: 'clock' }
      },
      {
        path: 'instructions',
        name: 'Instructions',
        component: () => import('@/admin/views/Instructions.vue'),
        meta: { title: '就诊须知', icon: 'document' }
      },
      {
        path: 'reports',
        name: 'Reports',
        component: () => import('@/admin/views/Reports.vue'),
        meta: { title: '报告管理', icon: 'files' }
      }
    ]
  }
]

const router = createRouter({
  history: createWebHashHistory(),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const userStore = useUserStore()

  // 设置页面标题
  document.title = to.meta.title ? `${to.meta.title} - 智影在线管理系统` : '智影在线管理系统'

  // 检查是否需要登录
  if (to.meta.requiresAuth) {
    if (!userStore.token) {
      next('/login')
    } else if (!userStore.isAdmin && !userStore.isStaff) {
      // 检查是否是管理员或员工
      next('/login')
    } else {
      next()
    }
  } else {
    next()
  }
})

export default router
