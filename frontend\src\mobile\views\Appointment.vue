<template>
  <div class="appointment">
    <van-nav-bar title="在线预约" left-arrow @click-left="$router.back()" />
    
    <div class="content">
      <van-form @submit="handleSubmit">
        <!-- 选择就诊人 -->
        <van-cell-group inset title="选择就诊人">
          <van-field
            v-model="selectedPatientName"
            name="patient"
            label="就诊人"
            placeholder="请选择就诊人"
            readonly
            is-link
            @click="showPatientPicker = true"
            :rules="[{ required: true, message: '请选择就诊人' }]"
          />
        </van-cell-group>

        <!-- 选择检查项目 -->
        <van-cell-group inset title="选择检查项目">
          <van-field name="project" label="检查项目">
            <template #input>
              <van-radio-group v-model="form.projectId">
                <van-radio
                  v-for="project in projects"
                  :key="project.id"
                  :name="project.id"
                  class="project-radio"
                >
                  <div class="project-info">
                    <div class="project-name">{{ project.name }}</div>
                    <div class="project-desc">{{ project.description }}</div>
                  </div>
                </van-radio>
              </van-radio-group>
            </template>
          </van-field>
        </van-cell-group>

        <!-- 选择预约时间 -->
        <van-cell-group inset title="选择预约时间">
          <van-field
            v-model="selectedDate"
            name="date"
            label="预约日期"
            placeholder="请选择日期"
            readonly
            is-link
            @click="showDatePicker = true"
            :rules="[{ required: true, message: '请选择预约日期' }]"
          />
          
          <van-field
            v-model="selectedTime"
            name="time"
            label="预约时间"
            placeholder="请选择时间"
            readonly
            is-link
            @click="showTimePicker = true"
            :rules="[{ required: true, message: '请选择预约时间' }]"
          />
        </van-cell-group>

        <!-- 备注信息 -->
        <van-cell-group inset title="备注信息">
          <van-field
            v-model="form.remarks"
            name="remarks"
            label="备注"
            type="textarea"
            placeholder="请输入备注信息（可选）"
            rows="3"
            autosize
          />
        </van-cell-group>

        <!-- 提交按钮 -->
        <div class="submit-button">
          <van-button
            round
            block
            type="primary"
            native-type="submit"
            :loading="loading"
          >
            确认预约
          </van-button>
        </div>
      </van-form>
    </div>

    <!-- 就诊人选择器 -->
    <van-popup v-model:show="showPatientPicker" position="bottom">
      <van-picker
        :columns="patientColumns"
        @confirm="onPatientConfirm"
        @cancel="showPatientPicker = false"
      />
    </van-popup>

    <!-- 日期选择器 -->
    <van-popup v-model:show="showDatePicker" position="bottom">
      <van-date-picker
        v-model="currentDate"
        :min-date="minDate"
        :max-date="maxDate"
        @confirm="onDateConfirm"
        @cancel="showDatePicker = false"
      />
    </van-popup>

    <!-- 时间选择器 -->
    <van-popup v-model:show="showTimePicker" position="bottom">
      <van-picker
        :columns="timeSlotColumns"
        @confirm="onTimeConfirm"
        @cancel="showTimePicker = false"
      />
    </van-popup>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { getPatients } from '@/api/patient'
import { getProjects } from '@/api/common'
import { getAvailableTimeSlots, createAppointment } from '@/api/appointment'
import { showToast } from 'vant'
import dayjs from 'dayjs'

const router = useRouter()

const loading = ref(false)
const patients = ref([])
const projects = ref([])
const timeSlots = ref([])

const showPatientPicker = ref(false)
const showDatePicker = ref(false)
const showTimePicker = ref(false)

const currentDate = ref(new Date())
const minDate = ref(new Date())
const maxDate = ref(new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)) // 30天后

const form = ref({
  patientId: null,
  projectId: null,
  appointmentTime: '',
  remarks: ''
})

const selectedPatientName = computed(() => {
  const patient = patients.value.find(p => p.id === form.value.patientId)
  return patient ? patient.name : ''
})

const selectedDate = ref('')
const selectedTime = ref('')

const patientColumns = computed(() => {
  return patients.value.map(patient => ({
    text: `${patient.name} (${patient.gender})`,
    value: patient.id
  }))
})

const timeSlotColumns = computed(() => {
  return timeSlots.value
    .filter(slot => slot.isAvailable && slot.availableSlots > 0)
    .map(slot => ({
      text: `${slot.startTime.substring(0, 5)}-${slot.endTime.substring(0, 5)} (剩余${slot.availableSlots}个)`,
      value: slot
    }))
})

const loadPatients = async () => {
  try {
    patients.value = await getPatients()
  } catch (error) {
    console.error('获取就诊人列表失败:', error)
  }
}

const loadProjects = async () => {
  try {
    projects.value = await getProjects()
    if (projects.value.length > 0) {
      form.value.projectId = projects.value[0].id
    }
  } catch (error) {
    console.error('获取检查项目失败:', error)
  }
}

const loadTimeSlots = async (date) => {
  try {
    timeSlots.value = await getAvailableTimeSlots(dayjs(date).format('YYYY-MM-DD'))
  } catch (error) {
    console.error('获取时间段失败:', error)
    timeSlots.value = []
  }
}

const onPatientConfirm = ({ selectedValues }) => {
  form.value.patientId = selectedValues[0]
  showPatientPicker.value = false
}

const onDateConfirm = (value) => {
  currentDate.value = value
  selectedDate.value = dayjs(value).format('YYYY-MM-DD')
  selectedTime.value = ''
  loadTimeSlots(value)
  showDatePicker.value = false
}

const onTimeConfirm = ({ selectedValues }) => {
  const slot = selectedValues[0]
  selectedTime.value = `${slot.startTime.substring(0, 5)}-${slot.endTime.substring(0, 5)}`
  
  // 构建完整的预约时间
  const appointmentDate = dayjs(selectedDate.value).format('YYYY-MM-DD')
  const appointmentTime = `${appointmentDate}T${slot.startTime}`
  form.value.appointmentTime = appointmentTime
  
  showTimePicker.value = false
}

const handleSubmit = async () => {
  if (!form.value.patientId) {
    showToast('请选择就诊人')
    return
  }
  if (!form.value.projectId) {
    showToast('请选择检查项目')
    return
  }
  if (!form.value.appointmentTime) {
    showToast('请选择预约时间')
    return
  }

  loading.value = true
  try {
    await createAppointment(form.value)
    showToast.success('预约成功')
    router.push('/appointments')
  } catch (error) {
    console.error('预约失败:', error)
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  loadPatients()
  loadProjects()
})
</script>

<style scoped>
.appointment {
  background-color: #f7f8fa;
  min-height: 100vh;
}

.content {
  padding: 16px;
}

.project-radio {
  width: 100%;
  margin-bottom: 12px;
}

.project-info {
  flex: 1;
  margin-left: 8px;
}

.project-name {
  font-size: 16px;
  font-weight: 500;
  color: #323233;
}

.project-desc {
  font-size: 14px;
  color: #969799;
  margin-top: 4px;
}

.submit-button {
  margin-top: 32px;
}
</style>
