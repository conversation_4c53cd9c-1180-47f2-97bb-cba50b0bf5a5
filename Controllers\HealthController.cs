using Microsoft.AspNetCore.Mvc;
using System;
using ZyOnlineApi.DTOs;

namespace ZyOnlineApi.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class HealthController : ControllerBase
    {
        /// <summary>
        /// 健康检查接口
        /// </summary>
        [HttpGet]
        public ActionResult<ApiResponse<object>> HealthCheck()
        {
            var healthInfo = new
            {
                Status = "Healthy",
                Timestamp = DateTime.Now,
                Version = "1.0.0",
                Environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Production"
            };

            return Ok(ApiResponse<object>.SuccessResult(healthInfo, "系统运行正常"));
        }

        /// <summary>
        /// 数据库连接测试
        /// </summary>
        [HttpGet("database")]
        public ActionResult<ApiResponse<object>> DatabaseCheck()
        {
            try
            {
                // 这里可以添加数据库连接测试逻辑
                var dbInfo = new
                {
                    Status = "Connected",
                    Timestamp = DateTime.Now,
                    Message = "数据库连接正常"
                };

                return Ok(ApiResponse<object>.SuccessResult(dbInfo, "数据库连接正常"));
            }
            catch (Exception ex)
            {
                return BadRequest(ApiResponse<object>.ErrorResult($"数据库连接失败: {ex.Message}"));
            }
        }
    }
}
