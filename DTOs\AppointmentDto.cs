using System;
using System.ComponentModel.DataAnnotations;

namespace ZyOnlineApi.DTOs
{
    public class AppointmentDto
    {
        public int Id { get; set; }
        public int PatientId { get; set; }
        public string PatientName { get; set; }
        public string PatientPhone { get; set; }
        public int ProjectId { get; set; }
        public string ProjectName { get; set; }
        public DateTime AppointmentTime { get; set; }
        public string Status { get; set; }
        public string Remarks { get; set; }
        public DateTime CreatedAt { get; set; }
    }

    public class CreateAppointmentDto
    {
        [Required(ErrorMessage = "就诊人不能为空")]
        public int PatientId { get; set; }

        [Required(ErrorMessage = "检查项目不能为空")]
        public int ProjectId { get; set; }

        [Required(ErrorMessage = "预约时间不能为空")]
        public DateTime AppointmentTime { get; set; }

        public string Remarks { get; set; }
    }

    public class UpdateAppointmentStatusDto
    {
        [Required(ErrorMessage = "状态不能为空")]
        [RegularExpression("^(待检查|已完成|已取消)$", ErrorMessage = "状态值不正确")]
        public string Status { get; set; }

        public string Remarks { get; set; }
    }

    public class AppointmentQueryDto
    {
        public string Status { get; set; } // 全部、待检查、已完成
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public string PatientName { get; set; }
        public string PatientPhone { get; set; }
        public int PageIndex { get; set; } = 1;
        public int PageSize { get; set; } = 10;
    }
}
