import request from '@/utils/request'

// 后台管理员登录
export function adminLogin(data) {
  return request({
    url: '/auth/login',
    method: 'post',
    data
  })
}

// 微信用户登录
export function wechatLogin(data) {
  return request({
    url: '/auth/wechat-login',
    method: 'post',
    data
  })
}

// 获取当前用户信息
export function getCurrentUser() {
  return request({
    url: '/auth/me',
    method: 'get'
  })
}
