using System.Collections.Generic;
using System.Threading.Tasks;
using ZyOnlineApi.DTOs;

namespace ZyOnlineApi.Services
{
    public interface IAppointmentService
    {
        Task<List<AppointmentDto>> GetAppointmentsByWechatOpenIdAsync(string wechatOpenId);
        Task<AppointmentDto> GetAppointmentByIdAsync(int id);
        Task<AppointmentDto> CreateAppointmentAsync(CreateAppointmentDto createAppointmentDto, string wechatOpenId);
        Task<bool> CancelAppointmentAsync(int id, string wechatOpenId);
        Task<PagedResult<AppointmentDto>> GetAppointmentsAsync(AppointmentQueryDto query);
        Task<AppointmentDto> UpdateAppointmentStatusAsync(int id, UpdateAppointmentStatusDto updateStatusDto);
        Task<List<AvailableTimeSlotDto>> GetAvailableTimeSlotsAsync(DateTime date);
        Task UpdateAppointmentStatusesAsync(); // 自动更新过期预约状态
    }
}
