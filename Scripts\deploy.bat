@echo off
chcp 65001 >nul

echo 开始部署智影在线服务...

REM 检查 .NET Core 3.1 是否安装
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: .NET Core 3.1 未安装，请先安装 .NET Core 3.1 SDK
    pause
    exit /b 1
)

REM 还原 NuGet 包
echo 正在还原 NuGet 包...
dotnet restore

if %errorlevel% neq 0 (
    echo 错误: NuGet 包还原失败
    pause
    exit /b 1
)

REM 构建项目
echo 正在构建项目...
dotnet build -c Release

if %errorlevel% neq 0 (
    echo 错误: 项目构建失败
    pause
    exit /b 1
)

REM 检查是否存在迁移文件
if exist "Migrations" (
    echo 正在应用数据库迁移...
    dotnet ef database update
    
    if %errorlevel% neq 0 (
        echo 警告: 数据库迁移失败，请手动执行数据库初始化脚本
    )
) else (
    echo 未找到迁移文件，请手动执行数据库初始化脚本: Scripts\database_init.sql
)

REM 发布项目
echo 正在发布项目...
dotnet publish -c Release -o .\publish

if %errorlevel% neq 0 (
    echo 错误: 项目发布失败
    pause
    exit /b 1
)

echo.
echo 部署完成！
echo 发布文件位于: .\publish
echo 请确保：
echo 1. MySQL 数据库已创建并可访问
echo 2. 配置文件中的数据库连接字符串正确
echo 3. 已执行数据库初始化脚本
echo.
echo 启动应用程序：
echo cd publish ^&^& dotnet ZyOnlineApi.dll
echo.
echo 或使用 Docker：
echo docker-compose up -d

pause
