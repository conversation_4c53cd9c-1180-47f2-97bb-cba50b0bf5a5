{"version": 3, "targets": {".NETCoreApp,Version=v3.1": {"AutoMapper/10.1.1": {"type": "package", "dependencies": {"Microsoft.CSharp": "4.7.0", "System.Reflection.Emit": "4.7.0"}, "compile": {"lib/netstandard2.0/AutoMapper.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/AutoMapper.dll": {"related": ".xml"}}}, "AutoMapper.Extensions.Microsoft.DependencyInjection/8.1.1": {"type": "package", "dependencies": {"AutoMapper": "[10.1.1, 11.0.0)", "Microsoft.Extensions.DependencyInjection.Abstractions": "3.0.0", "Microsoft.Extensions.Options": "3.0.0"}, "compile": {"lib/netstandard2.0/AutoMapper.Extensions.Microsoft.DependencyInjection.dll": {}}, "runtime": {"lib/netstandard2.0/AutoMapper.Extensions.Microsoft.DependencyInjection.dll": {}}}, "BCrypt.Net-Next/4.0.3": {"type": "package", "compile": {"lib/netstandard2.1/BCrypt.Net-Next.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/BCrypt.Net-Next.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Authentication.JwtBearer/3.1.32": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Protocols.OpenIdConnect": "5.5.0"}, "compile": {"lib/netcoreapp3.1/Microsoft.AspNetCore.Authentication.JwtBearer.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.1/Microsoft.AspNetCore.Authentication.JwtBearer.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.AspNetCore.App"]}, "Microsoft.AspNetCore.Cors/2.2.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Http.Extensions": "2.2.0", "Microsoft.Extensions.Configuration.Abstractions": "2.2.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "2.2.0", "Microsoft.Extensions.Options": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Cors.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Cors.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Http.Abstractions/2.2.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Http.Features": "2.2.0", "System.Text.Encodings.Web": "4.5.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Http.Extensions/2.2.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.Extensions.FileProviders.Abstractions": "2.2.0", "Microsoft.Net.Http.Headers": "2.2.0", "System.Buffers": "4.5.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Extensions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Extensions.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Http.Features/2.2.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Features.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Features.dll": {"related": ".xml"}}}, "Microsoft.Bcl.AsyncInterfaces/1.1.1": {"type": "package", "compile": {"ref/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {}}, "runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"related": ".xml"}}}, "Microsoft.Bcl.HashCode/1.1.1": {"type": "package", "compile": {"ref/netcoreapp2.1/Microsoft.Bcl.HashCode.dll": {}}, "runtime": {"lib/netcoreapp2.1/Microsoft.Bcl.HashCode.dll": {"related": ".xml"}}}, "Microsoft.CSharp/4.7.0": {"type": "package", "compile": {"ref/netcoreapp2.0/_._": {}}, "runtime": {"lib/netcoreapp2.0/_._": {}}}, "Microsoft.EntityFrameworkCore/3.1.32": {"type": "package", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": "1.1.1", "Microsoft.Bcl.HashCode": "1.1.1", "Microsoft.EntityFrameworkCore.Abstractions": "3.1.32", "Microsoft.EntityFrameworkCore.Analyzers": "3.1.32", "Microsoft.Extensions.Caching.Memory": "3.1.32", "Microsoft.Extensions.DependencyInjection": "3.1.32", "Microsoft.Extensions.Logging": "3.1.32", "System.Collections.Immutable": "1.7.1", "System.ComponentModel.Annotations": "4.7.0", "System.Diagnostics.DiagnosticSource": "4.7.1"}, "compile": {"lib/netstandard2.0/Microsoft.EntityFrameworkCore.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.EntityFrameworkCore.dll": {"related": ".xml"}}}, "Microsoft.EntityFrameworkCore.Abstractions/3.1.32": {"type": "package", "compile": {"lib/netstandard2.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.EntityFrameworkCore.Analyzers/3.1.32": {"type": "package", "compile": {"lib/netstandard2.0/_._": {}}, "runtime": {"lib/netstandard2.0/_._": {}}}, "Microsoft.EntityFrameworkCore.Design/3.1.32": {"type": "package", "dependencies": {"Microsoft.CSharp": "4.7.0", "Microsoft.EntityFrameworkCore.Relational": "3.1.32"}, "compile": {"lib/netstandard2.0/Microsoft.EntityFrameworkCore.Design.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.EntityFrameworkCore.Design.dll": {"related": ".xml"}}, "build": {"build/netcoreapp2.0/Microsoft.EntityFrameworkCore.Design.props": {}}}, "Microsoft.EntityFrameworkCore.Relational/3.1.32": {"type": "package", "dependencies": {"Microsoft.EntityFrameworkCore": "3.1.32"}, "compile": {"lib/netstandard2.0/Microsoft.EntityFrameworkCore.Relational.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.EntityFrameworkCore.Relational.dll": {"related": ".xml"}}}, "Microsoft.Extensions.ApiDescription.Server/3.0.0": {"type": "package", "build": {"build/Microsoft.Extensions.ApiDescription.Server.props": {}, "build/Microsoft.Extensions.ApiDescription.Server.targets": {}}, "buildMultiTargeting": {"buildMultiTargeting/Microsoft.Extensions.ApiDescription.Server.props": {}, "buildMultiTargeting/Microsoft.Extensions.ApiDescription.Server.targets": {}}}, "Microsoft.Extensions.Caching.Abstractions/3.1.32": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "3.1.32"}, "compile": {"lib/netcoreapp3.1/Microsoft.Extensions.Caching.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.1/Microsoft.Extensions.Caching.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Caching.Memory/3.1.32": {"type": "package", "dependencies": {"Microsoft.Extensions.Caching.Abstractions": "3.1.32", "Microsoft.Extensions.DependencyInjection.Abstractions": "3.1.32", "Microsoft.Extensions.Logging.Abstractions": "3.1.32", "Microsoft.Extensions.Options": "3.1.32"}, "compile": {"lib/netcoreapp3.1/Microsoft.Extensions.Caching.Memory.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.1/Microsoft.Extensions.Caching.Memory.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Configuration/3.1.32": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "3.1.32"}, "compile": {"lib/netcoreapp3.1/Microsoft.Extensions.Configuration.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.1/Microsoft.Extensions.Configuration.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Configuration.Abstractions/3.1.32": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "3.1.32"}, "compile": {"lib/netcoreapp3.1/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.1/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Configuration.Binder/3.1.32": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "3.1.32"}, "compile": {"lib/netcoreapp3.1/Microsoft.Extensions.Configuration.Binder.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.1/Microsoft.Extensions.Configuration.Binder.dll": {"related": ".xml"}}}, "Microsoft.Extensions.DependencyInjection/3.1.32": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "3.1.32"}, "compile": {"lib/netcoreapp3.1/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.1/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/3.1.32": {"type": "package", "compile": {"lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.Extensions.FileProviders.Abstractions/2.2.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Logging/3.1.32": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Binder": "3.1.32", "Microsoft.Extensions.DependencyInjection": "3.1.32", "Microsoft.Extensions.Logging.Abstractions": "3.1.32", "Microsoft.Extensions.Options": "3.1.32"}, "compile": {"lib/netcoreapp3.1/Microsoft.Extensions.Logging.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.1/Microsoft.Extensions.Logging.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Logging.Abstractions/3.1.32": {"type": "package", "compile": {"lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Options/3.1.32": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "3.1.32", "Microsoft.Extensions.Primitives": "3.1.32"}, "compile": {"lib/netcoreapp3.1/Microsoft.Extensions.Options.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.1/Microsoft.Extensions.Options.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Primitives/3.1.32": {"type": "package", "compile": {"lib/netcoreapp3.1/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.1/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.JsonWebTokens/6.10.0": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Tokens": "6.10.0"}, "compile": {"lib/netstandard2.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Logging/6.10.0": {"type": "package", "compile": {"lib/netstandard2.0/Microsoft.IdentityModel.Logging.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.Logging.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Protocols/5.5.0": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Logging": "5.5.0", "Microsoft.IdentityModel.Tokens": "5.5.0"}, "compile": {"lib/netstandard2.0/Microsoft.IdentityModel.Protocols.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.Protocols.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/5.5.0": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Protocols": "5.5.0", "Newtonsoft.Json": "10.0.1", "System.IdentityModel.Tokens.Jwt": "5.5.0"}, "compile": {"lib/netstandard2.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Tokens/6.10.0": {"type": "package", "dependencies": {"Microsoft.CSharp": "4.5.0", "Microsoft.IdentityModel.Logging": "6.10.0", "System.Security.Cryptography.Cng": "4.5.0"}, "compile": {"lib/netstandard2.0/Microsoft.IdentityModel.Tokens.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.Tokens.dll": {"related": ".xml"}}}, "Microsoft.Net.Http.Headers/2.2.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "2.2.0", "System.Buffers": "4.5.0"}, "compile": {"lib/netstandard2.0/Microsoft.Net.Http.Headers.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Net.Http.Headers.dll": {"related": ".xml"}}}, "Microsoft.OpenApi/1.2.3": {"type": "package", "compile": {"lib/netstandard2.0/Microsoft.OpenApi.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.OpenApi.dll": {"related": ".pdb;.xml"}}}, "MySqlConnector/0.69.10": {"type": "package", "compile": {"lib/netcoreapp3.0/MySqlConnector.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.0/MySqlConnector.dll": {"related": ".xml"}}}, "Newtonsoft.Json/11.0.2": {"type": "package", "compile": {"lib/netstandard2.0/Newtonsoft.Json.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Newtonsoft.Json.dll": {"related": ".xml"}}}, "Pomelo.EntityFrameworkCore.MySql/3.2.7": {"type": "package", "dependencies": {"Microsoft.EntityFrameworkCore.Relational": "[3.1.19, 5.0.0)", "MySqlConnector": "[0.69.10, 1.0.0)", "Pomelo.JsonObject": "2.2.1"}, "compile": {"lib/netstandard2.0/Pomelo.EntityFrameworkCore.MySql.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Pomelo.EntityFrameworkCore.MySql.dll": {"related": ".xml"}}}, "Pomelo.JsonObject/2.2.1": {"type": "package", "dependencies": {"Microsoft.CSharp": "4.5.0", "Newtonsoft.Json": "11.0.2"}, "compile": {"lib/netstandard2.0/Pomelo.JsonObject.dll": {}}, "runtime": {"lib/netstandard2.0/Pomelo.JsonObject.dll": {}}}, "Swashbuckle.AspNetCore/5.6.3": {"type": "package", "dependencies": {"Microsoft.Extensions.ApiDescription.Server": "3.0.0", "Swashbuckle.AspNetCore.Swagger": "5.6.3", "Swashbuckle.AspNetCore.SwaggerGen": "5.6.3", "Swashbuckle.AspNetCore.SwaggerUI": "5.6.3"}, "build": {"build/Swashbuckle.AspNetCore.props": {}}}, "Swashbuckle.AspNetCore.Swagger/5.6.3": {"type": "package", "dependencies": {"Microsoft.OpenApi": "1.2.3"}, "compile": {"lib/netcoreapp3.0/Swashbuckle.AspNetCore.Swagger.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netcoreapp3.0/Swashbuckle.AspNetCore.Swagger.dll": {"related": ".pdb;.xml"}}, "frameworkReferences": ["Microsoft.AspNetCore.App"]}, "Swashbuckle.AspNetCore.SwaggerGen/5.6.3": {"type": "package", "dependencies": {"Swashbuckle.AspNetCore.Swagger": "5.6.3"}, "compile": {"lib/netcoreapp3.0/Swashbuckle.AspNetCore.SwaggerGen.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netcoreapp3.0/Swashbuckle.AspNetCore.SwaggerGen.dll": {"related": ".pdb;.xml"}}, "frameworkReferences": ["Microsoft.AspNetCore.App"]}, "Swashbuckle.AspNetCore.SwaggerUI/5.6.3": {"type": "package", "compile": {"lib/netcoreapp3.0/Swashbuckle.AspNetCore.SwaggerUI.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netcoreapp3.0/Swashbuckle.AspNetCore.SwaggerUI.dll": {"related": ".pdb;.xml"}}, "frameworkReferences": ["Microsoft.AspNetCore.App"]}, "System.Buffers/4.5.0": {"type": "package", "compile": {"ref/netcoreapp2.0/_._": {}}, "runtime": {"lib/netcoreapp2.0/_._": {}}}, "System.Collections.Immutable/1.7.1": {"type": "package", "compile": {"lib/netstandard2.0/System.Collections.Immutable.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Collections.Immutable.dll": {"related": ".xml"}}}, "System.ComponentModel.Annotations/4.7.0": {"type": "package", "compile": {"ref/netstandard2.1/System.ComponentModel.Annotations.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/System.ComponentModel.Annotations.dll": {"related": ".xml"}}}, "System.Diagnostics.DiagnosticSource/4.7.1": {"type": "package", "compile": {"lib/netstandard1.3/System.Diagnostics.DiagnosticSource.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Diagnostics.DiagnosticSource.dll": {"related": ".xml"}}}, "System.IdentityModel.Tokens.Jwt/6.10.0": {"type": "package", "dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "6.10.0", "Microsoft.IdentityModel.Tokens": "6.10.0"}, "compile": {"lib/netstandard2.0/System.IdentityModel.Tokens.Jwt.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.IdentityModel.Tokens.Jwt.dll": {"related": ".xml"}}}, "System.Reflection.Emit/4.7.0": {"type": "package", "compile": {"ref/netcoreapp2.0/_._": {}}, "runtime": {"lib/netcoreapp2.0/_._": {}}}, "System.Security.Cryptography.Cng/4.5.0": {"type": "package", "compile": {"ref/netcoreapp2.1/System.Security.Cryptography.Cng.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp2.1/System.Security.Cryptography.Cng.dll": {}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp2.1/System.Security.Cryptography.Cng.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Text.Encodings.Web/4.5.0": {"type": "package", "compile": {"lib/netstandard2.0/System.Text.Encodings.Web.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Text.Encodings.Web.dll": {"related": ".xml"}}}}}, "libraries": {"AutoMapper/10.1.1": {"sha512": "uMgbqOdu9ZG5cIOty0C85hzzayBH2i9BthnS5FlMqKtMSHDv4ts81a2jS1VFaDBVhlBeIqJ/kQKjQY95BZde9w==", "type": "package", "path": "automapper/10.1.1", "files": [".nupkg.metadata", ".signature.p7s", "automapper.10.1.1.nupkg.sha512", "automapper.nuspec", "icon.png", "lib/net461/AutoMapper.dll", "lib/net461/AutoMapper.xml", "lib/netstandard2.0/AutoMapper.dll", "lib/netstandard2.0/AutoMapper.xml"]}, "AutoMapper.Extensions.Microsoft.DependencyInjection/8.1.1": {"sha512": "xSWoVzOipuDU4PeZcUfaZQ+xqXU8QmGv5jrdlxt3MYm9xaOmrefqcfzGQ3SQ+D+8wfBa/ZwSuL0qKOVj080inA==", "type": "package", "path": "automapper.extensions.microsoft.dependencyinjection/8.1.1", "files": [".nupkg.metadata", ".signature.p7s", "automapper.extensions.microsoft.dependencyinjection.8.1.1.nupkg.sha512", "automapper.extensions.microsoft.dependencyinjection.nuspec", "icon.png", "lib/netstandard2.0/AutoMapper.Extensions.Microsoft.DependencyInjection.dll"]}, "BCrypt.Net-Next/4.0.3": {"sha512": "W+U9WvmZQgi5cX6FS5GDtDoPzUCV4LkBLkywq/kRZhuDwcbavOzcDAr3LXJFqHUi952Yj3LEYoWW0jbEUQChsA==", "type": "package", "path": "bcrypt.net-next/4.0.3", "files": [".nupkg.metadata", ".signature.p7s", "bcrypt.net-next.4.0.3.nupkg.sha512", "bcrypt.net-next.nuspec", "ico.png", "lib/net20/BCrypt.Net-Next.dll", "lib/net20/BCrypt.Net-Next.xml", "lib/net35/BCrypt.Net-Next.dll", "lib/net35/BCrypt.Net-Next.xml", "lib/net462/BCrypt.Net-Next.dll", "lib/net462/BCrypt.Net-Next.xml", "lib/net472/BCrypt.Net-Next.dll", "lib/net472/BCrypt.Net-Next.xml", "lib/net48/BCrypt.Net-Next.dll", "lib/net48/BCrypt.Net-Next.xml", "lib/net5.0/BCrypt.Net-Next.dll", "lib/net5.0/BCrypt.Net-Next.xml", "lib/net6.0/BCrypt.Net-Next.dll", "lib/net6.0/BCrypt.Net-Next.xml", "lib/netstandard2.0/BCrypt.Net-Next.dll", "lib/netstandard2.0/BCrypt.Net-Next.xml", "lib/netstandard2.1/BCrypt.Net-Next.dll", "lib/netstandard2.1/BCrypt.Net-Next.xml", "readme.md"]}, "Microsoft.AspNetCore.Authentication.JwtBearer/3.1.32": {"sha512": "tBnXyLLOm6VxJ51qXU2D7yzmcbXlLUh2YtTYFfNmGiznATcvO3d+e6Q0rRPFAKk92R6gP2JAu3FApM+6sfHjCQ==", "type": "package", "path": "microsoft.aspnetcore.authentication.jwtbearer/3.1.32", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/netcoreapp3.1/Microsoft.AspNetCore.Authentication.JwtBearer.dll", "lib/netcoreapp3.1/Microsoft.AspNetCore.Authentication.JwtBearer.xml", "microsoft.aspnetcore.authentication.jwtbearer.3.1.32.nupkg.sha512", "microsoft.aspnetcore.authentication.jwtbearer.nuspec"]}, "Microsoft.AspNetCore.Cors/2.2.0": {"sha512": "LFlTM3ThS3ZCILuKnjy8HyK9/IlDh3opogdbCVx6tMGyDzTQBgMPXLjGDLtMk5QmLDCcP3l1TO3z/+1viA8GUg==", "type": "package", "path": "microsoft.aspnetcore.cors/2.2.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Cors.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Cors.xml", "microsoft.aspnetcore.cors.2.2.0.nupkg.sha512", "microsoft.aspnetcore.cors.nuspec"]}, "Microsoft.AspNetCore.Http.Abstractions/2.2.0": {"sha512": "Nxs7Z1q3f1STfLYKJSVXCs1iBl+Ya6E8o4Oy1bCxJ/rNI44E/0f6tbsrVqAWfB7jlnJfyaAtIalBVxPKUPQb4Q==", "type": "package", "path": "microsoft.aspnetcore.http.abstractions/2.2.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Http.Abstractions.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Http.Abstractions.xml", "microsoft.aspnetcore.http.abstractions.2.2.0.nupkg.sha512", "microsoft.aspnetcore.http.abstractions.nuspec"]}, "Microsoft.AspNetCore.Http.Extensions/2.2.0": {"sha512": "2DgZ9rWrJtuR7RYiew01nGRzuQBDaGHGmK56Rk54vsLLsCdzuFUPqbDTJCS1qJQWTbmbIQ9wGIOjpxA1t0l7/w==", "type": "package", "path": "microsoft.aspnetcore.http.extensions/2.2.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Http.Extensions.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Http.Extensions.xml", "microsoft.aspnetcore.http.extensions.2.2.0.nupkg.sha512", "microsoft.aspnetcore.http.extensions.nuspec"]}, "Microsoft.AspNetCore.Http.Features/2.2.0": {"sha512": "ziFz5zH8f33En4dX81LW84I6XrYXKf9jg6aM39cM+LffN9KJahViKZ61dGMSO2gd3e+qe5yBRwsesvyqlZaSMg==", "type": "package", "path": "microsoft.aspnetcore.http.features/2.2.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Http.Features.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Http.Features.xml", "microsoft.aspnetcore.http.features.2.2.0.nupkg.sha512", "microsoft.aspnetcore.http.features.nuspec"]}, "Microsoft.Bcl.AsyncInterfaces/1.1.1": {"sha512": "yuvf07qFWFqtK3P/MRkEKLhn5r2UbSpVueRziSqj0yJQIKFwG1pq9mOayK3zE5qZCTs0CbrwL9M6R8VwqyGy2w==", "type": "package", "path": "microsoft.bcl.asyncinterfaces/1.1.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Bcl.AsyncInterfaces.dll", "lib/net461/Microsoft.Bcl.AsyncInterfaces.xml", "lib/netstandard2.0/Microsoft.Bcl.AsyncInterfaces.dll", "lib/netstandard2.0/Microsoft.Bcl.AsyncInterfaces.xml", "lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll", "lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.xml", "microsoft.bcl.asyncinterfaces.1.1.1.nupkg.sha512", "microsoft.bcl.asyncinterfaces.nuspec", "ref/net461/Microsoft.Bcl.AsyncInterfaces.dll", "ref/netstandard2.0/Microsoft.Bcl.AsyncInterfaces.dll", "ref/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Bcl.HashCode/1.1.1": {"sha512": "MalY0Y/uM/LjXtHfX/26l2VtN4LDNZ2OE3aumNOHDLsT4fNYy2hiHXI4CXCqKpNUNm7iJ2brrc4J89UdaL56FA==", "type": "package", "path": "microsoft.bcl.hashcode/1.1.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Bcl.HashCode.dll", "lib/net461/Microsoft.Bcl.HashCode.xml", "lib/netcoreapp2.1/Microsoft.Bcl.HashCode.dll", "lib/netcoreapp2.1/Microsoft.Bcl.HashCode.xml", "lib/netstandard2.0/Microsoft.Bcl.HashCode.dll", "lib/netstandard2.0/Microsoft.Bcl.HashCode.xml", "lib/netstandard2.1/Microsoft.Bcl.HashCode.dll", "lib/netstandard2.1/Microsoft.Bcl.HashCode.xml", "microsoft.bcl.hashcode.1.1.1.nupkg.sha512", "microsoft.bcl.hashcode.nuspec", "ref/net461/Microsoft.Bcl.HashCode.dll", "ref/netcoreapp2.1/Microsoft.Bcl.HashCode.dll", "ref/netstandard2.0/Microsoft.Bcl.HashCode.dll", "ref/netstandard2.1/Microsoft.Bcl.HashCode.dll", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.CSharp/4.7.0": {"sha512": "pTj+D3uJWyN3My70i2Hqo+OXixq3Os2D1nJ2x92FFo6sk8fYS1m1WLNTs0Dc1uPaViH0YvEEwvzddQ7y4rhXmA==", "type": "package", "path": "microsoft.csharp/4.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/Microsoft.CSharp.dll", "lib/netcoreapp2.0/_._", "lib/netstandard1.3/Microsoft.CSharp.dll", "lib/netstandard2.0/Microsoft.CSharp.dll", "lib/netstandard2.0/Microsoft.CSharp.xml", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/uap10.0.16299/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "microsoft.csharp.4.7.0.nupkg.sha512", "microsoft.csharp.nuspec", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/Microsoft.CSharp.dll", "ref/netcore50/Microsoft.CSharp.xml", "ref/netcore50/de/Microsoft.CSharp.xml", "ref/netcore50/es/Microsoft.CSharp.xml", "ref/netcore50/fr/Microsoft.CSharp.xml", "ref/netcore50/it/Microsoft.CSharp.xml", "ref/netcore50/ja/Microsoft.CSharp.xml", "ref/netcore50/ko/Microsoft.CSharp.xml", "ref/netcore50/ru/Microsoft.CSharp.xml", "ref/netcore50/zh-hans/Microsoft.CSharp.xml", "ref/netcore50/zh-hant/Microsoft.CSharp.xml", "ref/netcoreapp2.0/_._", "ref/netstandard1.0/Microsoft.CSharp.dll", "ref/netstandard1.0/Microsoft.CSharp.xml", "ref/netstandard1.0/de/Microsoft.CSharp.xml", "ref/netstandard1.0/es/Microsoft.CSharp.xml", "ref/netstandard1.0/fr/Microsoft.CSharp.xml", "ref/netstandard1.0/it/Microsoft.CSharp.xml", "ref/netstandard1.0/ja/Microsoft.CSharp.xml", "ref/netstandard1.0/ko/Microsoft.CSharp.xml", "ref/netstandard1.0/ru/Microsoft.CSharp.xml", "ref/netstandard1.0/zh-hans/Microsoft.CSharp.xml", "ref/netstandard1.0/zh-hant/Microsoft.CSharp.xml", "ref/netstandard2.0/Microsoft.CSharp.dll", "ref/netstandard2.0/Microsoft.CSharp.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/uap10.0.16299/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.EntityFrameworkCore/3.1.32": {"sha512": "I6C1GTTg9xS/eLEIaea5m3vIKMZLQAt9RU6QnAoYTJDS03NHXXl8xNgsdcN4a5Zyc8hvnWPbYAW18ONLcG5+AQ==", "type": "package", "path": "microsoft.entityframeworkcore/3.1.32", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "lib/netstandard2.0/Microsoft.EntityFrameworkCore.dll", "lib/netstandard2.0/Microsoft.EntityFrameworkCore.xml", "microsoft.entityframeworkcore.3.1.32.nupkg.sha512", "microsoft.entityframeworkcore.nuspec"]}, "Microsoft.EntityFrameworkCore.Abstractions/3.1.32": {"sha512": "4y0VcTXWIQIWduWSFl+mZcESPQBlIPlZMeP221YvWmkQnHt1iHDHaaNBilqGAH6q1P0SvHfHDOqwVXpppiztbw==", "type": "package", "path": "microsoft.entityframeworkcore.abstractions/3.1.32", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "lib/netstandard2.0/Microsoft.EntityFrameworkCore.Abstractions.dll", "lib/netstandard2.0/Microsoft.EntityFrameworkCore.Abstractions.xml", "microsoft.entityframeworkcore.abstractions.3.1.32.nupkg.sha512", "microsoft.entityframeworkcore.abstractions.nuspec"]}, "Microsoft.EntityFrameworkCore.Analyzers/3.1.32": {"sha512": "yvQBsos0/oAqN9rjcTRAFflBRcgFd8JINb9m0Zl7y12ac5jswvD8ILNVDSu5UCAd1y84xehjRKcq2ptIceBeGg==", "type": "package", "path": "microsoft.entityframeworkcore.analyzers/3.1.32", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "analyzers/dotnet/cs/Microsoft.EntityFrameworkCore.Analyzers.dll", "lib/netstandard2.0/_._", "microsoft.entityframeworkcore.analyzers.3.1.32.nupkg.sha512", "microsoft.entityframeworkcore.analyzers.nuspec"]}, "Microsoft.EntityFrameworkCore.Design/3.1.32": {"sha512": "j3J9cHpTBi1zoOVyOsDT27bTZENhqsOij+EPfRWxojxgo5qn3Ayp+xdtxM1E37av7oy4yB+DJTY1pOKYWJtAOQ==", "type": "package", "path": "microsoft.entityframeworkcore.design/3.1.32", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "build/net461/Microsoft.EntityFrameworkCore.Design.props", "build/netcoreapp2.0/Microsoft.EntityFrameworkCore.Design.props", "lib/netstandard2.0/Microsoft.EntityFrameworkCore.Design.dll", "lib/netstandard2.0/Microsoft.EntityFrameworkCore.Design.xml", "microsoft.entityframeworkcore.design.3.1.32.nupkg.sha512", "microsoft.entityframeworkcore.design.nuspec"]}, "Microsoft.EntityFrameworkCore.Relational/3.1.32": {"sha512": "ex5Dr6GzZdzAiJoGRPxubSDLMSIzmMa4Cxea4eFMb8tSvfv+AUhlKIasU7WMq0v9qlqudbf5tiUAdhoehiv+iw==", "type": "package", "path": "microsoft.entityframeworkcore.relational/3.1.32", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "lib/netstandard2.0/Microsoft.EntityFrameworkCore.Relational.dll", "lib/netstandard2.0/Microsoft.EntityFrameworkCore.Relational.xml", "microsoft.entityframeworkcore.relational.3.1.32.nupkg.sha512", "microsoft.entityframeworkcore.relational.nuspec"]}, "Microsoft.Extensions.ApiDescription.Server/3.0.0": {"sha512": "LH4OE/76F6sOCslif7+Xh3fS/wUUrE5ryeXAMcoCnuwOQGT5Smw0p57IgDh/pHgHaGz/e+AmEQb7pRgb++wt0w==", "type": "package", "path": "microsoft.extensions.apidescription.server/3.0.0", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "build/Microsoft.Extensions.ApiDescription.Server.props", "build/Microsoft.Extensions.ApiDescription.Server.targets", "buildMultiTargeting/Microsoft.Extensions.ApiDescription.Server.props", "buildMultiTargeting/Microsoft.Extensions.ApiDescription.Server.targets", "microsoft.extensions.apidescription.server.3.0.0.nupkg.sha512", "microsoft.extensions.apidescription.server.nuspec", "tools/Newtonsoft.Json.dll", "tools/dotnet-getdocument.deps.json", "tools/dotnet-getdocument.dll", "tools/dotnet-getdocument.runtimeconfig.json", "tools/net461-x86/GetDocument.Insider.exe", "tools/net461-x86/GetDocument.Insider.exe.config", "tools/net461/GetDocument.Insider.exe", "tools/net461/GetDocument.Insider.exe.config", "tools/netcoreapp2.1/GetDocument.Insider.deps.json", "tools/netcoreapp2.1/GetDocument.Insider.dll", "tools/netcoreapp2.1/GetDocument.Insider.runtimeconfig.json"]}, "Microsoft.Extensions.Caching.Abstractions/3.1.32": {"sha512": "cZdm91JlYwshLdYtg01a/FIuAiKQf/K/pg8q3+qLBnSEwy5dbPQYnS+/1W8aQb5JVTpb/+ZH3BvKab6V0M9Y/w==", "type": "package", "path": "microsoft.extensions.caching.abstractions/3.1.32", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "lib/netcoreapp3.1/Microsoft.Extensions.Caching.Abstractions.dll", "lib/netcoreapp3.1/Microsoft.Extensions.Caching.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Caching.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Caching.Abstractions.xml", "microsoft.extensions.caching.abstractions.3.1.32.nupkg.sha512", "microsoft.extensions.caching.abstractions.nuspec"]}, "Microsoft.Extensions.Caching.Memory/3.1.32": {"sha512": "ANoYCSOuKSh7jYBhqhqLHb4aOEYhsqdKeyA3ralscLxaq6sftQZDia3wvebMYEoMjwxTBMwyEEKcDEb9+QQzKw==", "type": "package", "path": "microsoft.extensions.caching.memory/3.1.32", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "lib/netcoreapp3.1/Microsoft.Extensions.Caching.Memory.dll", "lib/netcoreapp3.1/Microsoft.Extensions.Caching.Memory.xml", "lib/netstandard2.0/Microsoft.Extensions.Caching.Memory.dll", "lib/netstandard2.0/Microsoft.Extensions.Caching.Memory.xml", "microsoft.extensions.caching.memory.3.1.32.nupkg.sha512", "microsoft.extensions.caching.memory.nuspec"]}, "Microsoft.Extensions.Configuration/3.1.32": {"sha512": "WuOHTU9FB1yHaIU+/Ar1s5swHshH+7YjU7eA9Lmv0kO+rta7xOrR5Xu68srdxNpE9HjqjzxGZhPJFLxpP3J1Og==", "type": "package", "path": "microsoft.extensions.configuration/3.1.32", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "lib/netcoreapp3.1/Microsoft.Extensions.Configuration.dll", "lib/netcoreapp3.1/Microsoft.Extensions.Configuration.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.xml", "microsoft.extensions.configuration.3.1.32.nupkg.sha512", "microsoft.extensions.configuration.nuspec"]}, "Microsoft.Extensions.Configuration.Abstractions/3.1.32": {"sha512": "w8WEwVFYbTkoDQ/eJgGUPiL4SqZOiIVBkGxbkmnJAWnFxRigFk4WZla/3MDkN9fGSis6JwJfc57YgnleTw48AA==", "type": "package", "path": "microsoft.extensions.configuration.abstractions/3.1.32", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "lib/netcoreapp3.1/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/netcoreapp3.1/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.xml", "microsoft.extensions.configuration.abstractions.3.1.32.nupkg.sha512", "microsoft.extensions.configuration.abstractions.nuspec"]}, "Microsoft.Extensions.Configuration.Binder/3.1.32": {"sha512": "e89Od7dtbQpSPL3wNLlZ5LYv6KtMczxr2KSTULt4mhhUnMJtSfDIe1JJJJaL/Ed2+cwyAXtkMpqf4aR/Sd4mTQ==", "type": "package", "path": "microsoft.extensions.configuration.binder/3.1.32", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "lib/netcoreapp3.1/Microsoft.Extensions.Configuration.Binder.dll", "lib/netcoreapp3.1/Microsoft.Extensions.Configuration.Binder.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Binder.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Binder.xml", "microsoft.extensions.configuration.binder.3.1.32.nupkg.sha512", "microsoft.extensions.configuration.binder.nuspec"]}, "Microsoft.Extensions.DependencyInjection/3.1.32": {"sha512": "VUbvtpsoHZf4XtBhsfio0+2cpqC9bJs6ZiApT3G81CwBfcDV5OSeU9SaI6it6wxaGf0K2uADzzIS+4yTADaTRg==", "type": "package", "path": "microsoft.extensions.dependencyinjection/3.1.32", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "lib/net461/Microsoft.Extensions.DependencyInjection.dll", "lib/net461/Microsoft.Extensions.DependencyInjection.xml", "lib/netcoreapp3.1/Microsoft.Extensions.DependencyInjection.dll", "lib/netcoreapp3.1/Microsoft.Extensions.DependencyInjection.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.xml", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.dll", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.xml", "microsoft.extensions.dependencyinjection.3.1.32.nupkg.sha512", "microsoft.extensions.dependencyinjection.nuspec"]}, "Microsoft.Extensions.DependencyInjection.Abstractions/3.1.32": {"sha512": "dARl3iAcHZshMvnXtKpLGES4QZq8ExxIQnF2s8pXfP3MJOOXRSBsk+UmA3TMVN4hPr4O2QFS8AMJp9GDD/4lDw==", "type": "package", "path": "microsoft.extensions.dependencyinjection.abstractions/3.1.32", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "microsoft.extensions.dependencyinjection.abstractions.3.1.32.nupkg.sha512", "microsoft.extensions.dependencyinjection.abstractions.nuspec"]}, "Microsoft.Extensions.FileProviders.Abstractions/2.2.0": {"sha512": "EcnaSsPTqx2MGnHrmWOD0ugbuuqVT8iICqSqPzi45V5/MA1LjUNb0kwgcxBGqizV1R+WeBK7/Gw25Jzkyk9bIw==", "type": "package", "path": "microsoft.extensions.fileproviders.abstractions/2.2.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.xml", "microsoft.extensions.fileproviders.abstractions.2.2.0.nupkg.sha512", "microsoft.extensions.fileproviders.abstractions.nuspec"]}, "Microsoft.Extensions.Logging/3.1.32": {"sha512": "5nx7SuLJnqINfv9zslFGig1Czt2AWAyKLiSEZj8eHxYJucuHcoenvD8p75FafYANsMMOach6ZiNZ1MdlPFS/MQ==", "type": "package", "path": "microsoft.extensions.logging/3.1.32", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "lib/netcoreapp3.1/Microsoft.Extensions.Logging.dll", "lib/netcoreapp3.1/Microsoft.Extensions.Logging.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.xml", "microsoft.extensions.logging.3.1.32.nupkg.sha512", "microsoft.extensions.logging.nuspec"]}, "Microsoft.Extensions.Logging.Abstractions/3.1.32": {"sha512": "BShtU4APsEGa72vaj42rHc8CW40xpFp1dousoNeziHwOUFJLNOJP2wxKX2jyfer0nFaLSkIMtgKn+qeJtS3Pcw==", "type": "package", "path": "microsoft.extensions.logging.abstractions/3.1.32", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.xml", "microsoft.extensions.logging.abstractions.3.1.32.nupkg.sha512", "microsoft.extensions.logging.abstractions.nuspec"]}, "Microsoft.Extensions.Options/3.1.32": {"sha512": "JRX2+OxOC/jqWrkyjJZstpe0NdY/HfOJNPOzVkMxqCxTirecIYfaKXAms4HDsEKl3i6CD8jDkwHyv3fyWZO6hQ==", "type": "package", "path": "microsoft.extensions.options/3.1.32", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "lib/netcoreapp3.1/Microsoft.Extensions.Options.dll", "lib/netcoreapp3.1/Microsoft.Extensions.Options.xml", "lib/netstandard2.0/Microsoft.Extensions.Options.dll", "lib/netstandard2.0/Microsoft.Extensions.Options.xml", "microsoft.extensions.options.3.1.32.nupkg.sha512", "microsoft.extensions.options.nuspec"]}, "Microsoft.Extensions.Primitives/3.1.32": {"sha512": "N8lTVwdjR+df9Sx3VdHfrecV6zl8KQoAx7kQXJ3rYwQBEw2vuZM0LKVAqjnaA/TBC8ZKnt99ptwH5iaEOxBuYQ==", "type": "package", "path": "microsoft.extensions.primitives/3.1.32", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "lib/netcoreapp3.1/Microsoft.Extensions.Primitives.dll", "lib/netcoreapp3.1/Microsoft.Extensions.Primitives.xml", "lib/netstandard2.0/Microsoft.Extensions.Primitives.dll", "lib/netstandard2.0/Microsoft.Extensions.Primitives.xml", "microsoft.extensions.primitives.3.1.32.nupkg.sha512", "microsoft.extensions.primitives.nuspec"]}, "Microsoft.IdentityModel.JsonWebTokens/6.10.0": {"sha512": "0qjS31rN1MQTc46tAYbzmMTSRfdV5ndZxSjYxIGqKSidd4wpNJfNII/pdhU5Fx8olarQoKL9lqqYw4yNOIwT0Q==", "type": "package", "path": "microsoft.identitymodel.jsonwebtokens/6.10.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net45/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/net45/Microsoft.IdentityModel.JsonWebTokens.xml", "lib/net461/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/net461/Microsoft.IdentityModel.JsonWebTokens.xml", "lib/net472/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/net472/Microsoft.IdentityModel.JsonWebTokens.xml", "lib/netstandard2.0/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/netstandard2.0/Microsoft.IdentityModel.JsonWebTokens.xml", "microsoft.identitymodel.jsonwebtokens.6.10.0.nupkg.sha512", "microsoft.identitymodel.jsonwebtokens.nuspec"]}, "Microsoft.IdentityModel.Logging/6.10.0": {"sha512": "zbcwV6esnNzhZZ/VP87dji6VrUBLB5rxnZBkDMqNYpyG+nrBnBsbm4PUYLCBMUflHCM9EMLDG0rLnqqT+l0ldA==", "type": "package", "path": "microsoft.identitymodel.logging/6.10.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net45/Microsoft.IdentityModel.Logging.dll", "lib/net45/Microsoft.IdentityModel.Logging.xml", "lib/net461/Microsoft.IdentityModel.Logging.dll", "lib/net461/Microsoft.IdentityModel.Logging.xml", "lib/net472/Microsoft.IdentityModel.Logging.dll", "lib/net472/Microsoft.IdentityModel.Logging.xml", "lib/netstandard2.0/Microsoft.IdentityModel.Logging.dll", "lib/netstandard2.0/Microsoft.IdentityModel.Logging.xml", "microsoft.identitymodel.logging.6.10.0.nupkg.sha512", "microsoft.identitymodel.logging.nuspec"]}, "Microsoft.IdentityModel.Protocols/5.5.0": {"sha512": "m1gwAQwZjUxzRBC+4H40vYSo9Cms9yUbMdW492rQoXHU77G/ItiKxpk2+W9bWYcdsKUDKudye7im3T3MlVxEkg==", "type": "package", "path": "microsoft.identitymodel.protocols/5.5.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net45/Microsoft.IdentityModel.Protocols.dll", "lib/net45/Microsoft.IdentityModel.Protocols.xml", "lib/net451/Microsoft.IdentityModel.Protocols.dll", "lib/net451/Microsoft.IdentityModel.Protocols.xml", "lib/net461/Microsoft.IdentityModel.Protocols.dll", "lib/net461/Microsoft.IdentityModel.Protocols.xml", "lib/netstandard1.4/Microsoft.IdentityModel.Protocols.dll", "lib/netstandard1.4/Microsoft.IdentityModel.Protocols.xml", "lib/netstandard2.0/Microsoft.IdentityModel.Protocols.dll", "lib/netstandard2.0/Microsoft.IdentityModel.Protocols.xml", "microsoft.identitymodel.protocols.5.5.0.nupkg.sha512", "microsoft.identitymodel.protocols.nuspec"]}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/5.5.0": {"sha512": "21F4QlbaD5CXNs2urNRCO6vljbbrhv3gmGT8P18SKGKZ9IYBCn29extoJriHiPfhABd5b8S7RcdKU50XhERkYg==", "type": "package", "path": "microsoft.identitymodel.protocols.openidconnect/5.5.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net45/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll", "lib/net45/Microsoft.IdentityModel.Protocols.OpenIdConnect.xml", "lib/net451/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll", "lib/net451/Microsoft.IdentityModel.Protocols.OpenIdConnect.xml", "lib/net461/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll", "lib/net461/Microsoft.IdentityModel.Protocols.OpenIdConnect.xml", "lib/netstandard1.4/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll", "lib/netstandard1.4/Microsoft.IdentityModel.Protocols.OpenIdConnect.xml", "lib/netstandard2.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll", "lib/netstandard2.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.xml", "microsoft.identitymodel.protocols.openidconnect.5.5.0.nupkg.sha512", "microsoft.identitymodel.protocols.openidconnect.nuspec"]}, "Microsoft.IdentityModel.Tokens/6.10.0": {"sha512": "qbf1NslutDB4oLrriYTJpy7oB1pbh2ej2lEHd2IPDQH9C74ysOdhU5wAC7KoXblldbo7YsNR2QYFOqQM/b0Rsg==", "type": "package", "path": "microsoft.identitymodel.tokens/6.10.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net45/Microsoft.IdentityModel.Tokens.dll", "lib/net45/Microsoft.IdentityModel.Tokens.xml", "lib/net461/Microsoft.IdentityModel.Tokens.dll", "lib/net461/Microsoft.IdentityModel.Tokens.xml", "lib/net472/Microsoft.IdentityModel.Tokens.dll", "lib/net472/Microsoft.IdentityModel.Tokens.xml", "lib/netstandard2.0/Microsoft.IdentityModel.Tokens.dll", "lib/netstandard2.0/Microsoft.IdentityModel.Tokens.xml", "microsoft.identitymodel.tokens.6.10.0.nupkg.sha512", "microsoft.identitymodel.tokens.nuspec"]}, "Microsoft.Net.Http.Headers/2.2.0": {"sha512": "iZNkjYqlo8sIOI0bQfpsSoMTmB/kyvmV2h225ihyZT33aTp48ZpF6qYnXxzSXmHt8DpBAwBTX+1s1UFLbYfZKg==", "type": "package", "path": "microsoft.net.http.headers/2.2.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.Net.Http.Headers.dll", "lib/netstandard2.0/Microsoft.Net.Http.Headers.xml", "microsoft.net.http.headers.2.2.0.nupkg.sha512", "microsoft.net.http.headers.nuspec"]}, "Microsoft.OpenApi/1.2.3": {"sha512": "Nug3rO+7Kl5/SBAadzSMAVgqDlfGjJZ0GenQrLywJ84XGKO0uRqkunz5Wyl0SDwcR71bAATXvSdbdzPrYRYKGw==", "type": "package", "path": "microsoft.openapi/1.2.3", "files": [".nupkg.metadata", ".signature.p7s", "lib/net46/Microsoft.OpenApi.dll", "lib/net46/Microsoft.OpenApi.pdb", "lib/net46/Microsoft.OpenApi.xml", "lib/netstandard2.0/Microsoft.OpenApi.dll", "lib/netstandard2.0/Microsoft.OpenApi.pdb", "lib/netstandard2.0/Microsoft.OpenApi.xml", "microsoft.openapi.1.2.3.nupkg.sha512", "microsoft.openapi.nuspec"]}, "MySqlConnector/0.69.10": {"sha512": "flikhWc6q1gZE4l1PujXLnoZxthf/DqKo43y8x5Cw7/iaivjVYAHHhlr3/t6i8GImi/dbxP4zntp5J/4EVFcbw==", "type": "package", "path": "mysqlconnector/0.69.10", "files": [".nupkg.metadata", ".signature.p7s", "lib/net45/MySqlConnector.dll", "lib/net45/MySqlConnector.xml", "lib/net461/MySqlConnector.dll", "lib/net461/MySqlConnector.xml", "lib/net471/MySqlConnector.dll", "lib/net471/MySqlConnector.xml", "lib/netcoreapp2.1/MySqlConnector.dll", "lib/netcoreapp2.1/MySqlConnector.xml", "lib/netcoreapp3.0/MySqlConnector.dll", "lib/netcoreapp3.0/MySqlConnector.xml", "lib/netstandard1.3/MySqlConnector.dll", "lib/netstandard1.3/MySqlConnector.xml", "lib/netstandard2.0/MySqlConnector.dll", "lib/netstandard2.0/MySqlConnector.xml", "lib/netstandard2.1/MySqlConnector.dll", "lib/netstandard2.1/MySqlConnector.xml", "logo.png", "mysqlconnector.0.69.10.nupkg.sha512", "mysqlconnector.nuspec"]}, "Newtonsoft.Json/11.0.2": {"sha512": "IvJe1pj7JHEsP8B8J8DwlMEx8UInrs/x+9oVY+oCD13jpLu4JbJU2WCIsMRn5C4yW9+DgkaO8uiVE5VHKjpmdQ==", "type": "package", "path": "newtonsoft.json/11.0.2", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.md", "lib/net20/Newtonsoft.Json.dll", "lib/net20/Newtonsoft.Json.xml", "lib/net35/Newtonsoft.Json.dll", "lib/net35/Newtonsoft.Json.xml", "lib/net40/Newtonsoft.Json.dll", "lib/net40/Newtonsoft.Json.xml", "lib/net45/Newtonsoft.Json.dll", "lib/net45/Newtonsoft.Json.xml", "lib/netstandard1.0/Newtonsoft.Json.dll", "lib/netstandard1.0/Newtonsoft.Json.xml", "lib/netstandard1.3/Newtonsoft.Json.dll", "lib/netstandard1.3/Newtonsoft.Json.xml", "lib/netstandard2.0/Newtonsoft.Json.dll", "lib/netstandard2.0/Newtonsoft.Json.xml", "lib/portable-net40+sl5+win8+wp8+wpa81/Newtonsoft.Json.dll", "lib/portable-net40+sl5+win8+wp8+wpa81/Newtonsoft.Json.xml", "lib/portable-net45+win8+wp8+wpa81/Newtonsoft.Json.dll", "lib/portable-net45+win8+wp8+wpa81/Newtonsoft.Json.xml", "newtonsoft.json.11.0.2.nupkg.sha512", "newtonsoft.json.nuspec"]}, "Pomelo.EntityFrameworkCore.MySql/3.2.7": {"sha512": "H3AxDVX+3owmmXtmjOiTZSM89ryCe+tPpDZU4MZh6W8UO4Y9dohLTEPTQTeq60wKYPwww/1yJiNtOrqYBRtvpw==", "type": "package", "path": "pomelo.entityframeworkcore.mysql/3.2.7", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "lib/netstandard2.0/Pomelo.EntityFrameworkCore.MySql.dll", "lib/netstandard2.0/Pomelo.EntityFrameworkCore.MySql.xml", "pomelo.entityframeworkcore.mysql.3.2.7.nupkg.sha512", "pomelo.entityframeworkcore.mysql.nuspec"]}, "Pomelo.JsonObject/2.2.1": {"sha512": "VHPk3Yf7nDt+tZMC1M4oAoc3bgTYsOrap3VTjn//vd91b/nfquAbAeq1k0Lf7mPt8J7imLd9Pbzm50uB5euuZA==", "type": "package", "path": "pomelo.jsonobject/2.2.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Pomelo.JsonObject.dll", "pomelo.jsonobject.2.2.1.nupkg.sha512", "pomelo.jsonobject.nuspec"]}, "Swashbuckle.AspNetCore/5.6.3": {"sha512": "UkL9GU0mfaA+7RwYjEaBFvAzL8qNQhNqAeV5uaWUu/Z+fVgvK9FHkGCpTXBqSQeIHuZaIElzxnLDdIqGzuCnVg==", "type": "package", "path": "swashbuckle.aspnetcore/5.6.3", "files": [".nupkg.metadata", ".signature.p7s", "build/Swashbuckle.AspNetCore.props", "swashbuckle.aspnetcore.5.6.3.nupkg.sha512", "swashbuckle.aspnetcore.nuspec"]}, "Swashbuckle.AspNetCore.Swagger/5.6.3": {"sha512": "rn/MmLscjg6WSnTZabojx5DQYle2GjPanSPbCU3Kw8Hy72KyQR3uy8R1Aew5vpNALjfUFm2M/vwUtqdOlzw+GA==", "type": "package", "path": "swashbuckle.aspnetcore.swagger/5.6.3", "files": [".nupkg.metadata", ".signature.p7s", "lib/netcoreapp3.0/Swashbuckle.AspNetCore.Swagger.dll", "lib/netcoreapp3.0/Swashbuckle.AspNetCore.Swagger.pdb", "lib/netcoreapp3.0/Swashbuckle.AspNetCore.Swagger.xml", "lib/netstandard2.0/Swashbuckle.AspNetCore.Swagger.dll", "lib/netstandard2.0/Swashbuckle.AspNetCore.Swagger.pdb", "lib/netstandard2.0/Swashbuckle.AspNetCore.Swagger.xml", "swashbuckle.aspnetcore.swagger.5.6.3.nupkg.sha512", "swashbuckle.aspnetcore.swagger.nuspec"]}, "Swashbuckle.AspNetCore.SwaggerGen/5.6.3": {"sha512": "CkhVeod/iLd3ikVTDOwG5sym8BE5xbqGJ15iF3cC7ZPg2kEwDQL4a88xjkzsvC9oOB2ax6B0rK0EgRK+eOBX+w==", "type": "package", "path": "swashbuckle.aspnetcore.swaggergen/5.6.3", "files": [".nupkg.metadata", ".signature.p7s", "lib/netcoreapp3.0/Swashbuckle.AspNetCore.SwaggerGen.dll", "lib/netcoreapp3.0/Swashbuckle.AspNetCore.SwaggerGen.pdb", "lib/netcoreapp3.0/Swashbuckle.AspNetCore.SwaggerGen.xml", "lib/netstandard2.0/Swashbuckle.AspNetCore.SwaggerGen.dll", "lib/netstandard2.0/Swashbuckle.AspNetCore.SwaggerGen.pdb", "lib/netstandard2.0/Swashbuckle.AspNetCore.SwaggerGen.xml", "swashbuckle.aspnetcore.swaggergen.5.6.3.nupkg.sha512", "swashbuckle.aspnetcore.swaggergen.nuspec"]}, "Swashbuckle.AspNetCore.SwaggerUI/5.6.3": {"sha512": "BPvcPxQRMsYZ3HnYmGKRWDwX4Wo29WHh14Q6B10BB8Yfbbcza+agOC2UrBFA1EuaZuOsFLbp6E2+mqVNF/Je8A==", "type": "package", "path": "swashbuckle.aspnetcore.swaggerui/5.6.3", "files": [".nupkg.metadata", ".signature.p7s", "lib/netcoreapp3.0/Swashbuckle.AspNetCore.SwaggerUI.dll", "lib/netcoreapp3.0/Swashbuckle.AspNetCore.SwaggerUI.pdb", "lib/netcoreapp3.0/Swashbuckle.AspNetCore.SwaggerUI.xml", "lib/netstandard2.0/Swashbuckle.AspNetCore.SwaggerUI.dll", "lib/netstandard2.0/Swashbuckle.AspNetCore.SwaggerUI.pdb", "lib/netstandard2.0/Swashbuckle.AspNetCore.SwaggerUI.xml", "swashbuckle.aspnetcore.swaggerui.5.6.3.nupkg.sha512", "swashbuckle.aspnetcore.swaggerui.nuspec"]}, "System.Buffers/4.5.0": {"sha512": "pL2ChpaRRWI/p4LXyy4RgeWlYF2sgfj/pnVMvBqwNFr5cXg7CXNnWZWxrOONLg8VGdFB8oB+EG2Qw4MLgTOe+A==", "type": "package", "path": "system.buffers/4.5.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/netcoreapp2.0/_._", "lib/netstandard1.1/System.Buffers.dll", "lib/netstandard1.1/System.Buffers.xml", "lib/netstandard2.0/System.Buffers.dll", "lib/netstandard2.0/System.Buffers.xml", "lib/uap10.0.16299/_._", "ref/net45/System.Buffers.dll", "ref/net45/System.Buffers.xml", "ref/netcoreapp2.0/_._", "ref/netstandard1.1/System.Buffers.dll", "ref/netstandard1.1/System.Buffers.xml", "ref/netstandard2.0/System.Buffers.dll", "ref/netstandard2.0/System.Buffers.xml", "ref/uap10.0.16299/_._", "system.buffers.4.5.0.nupkg.sha512", "system.buffers.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Collections.Immutable/1.7.1": {"sha512": "B43Zsz5EfMwyEbnObwRxW5u85fzJma3lrDeGcSAV1qkhSRTNY5uXAByTn9h9ddNdhM+4/YoLc/CI43umjwIl9Q==", "type": "package", "path": "system.collections.immutable/1.7.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Collections.Immutable.dll", "lib/net461/System.Collections.Immutable.xml", "lib/netstandard1.0/System.Collections.Immutable.dll", "lib/netstandard1.0/System.Collections.Immutable.xml", "lib/netstandard1.3/System.Collections.Immutable.dll", "lib/netstandard1.3/System.Collections.Immutable.xml", "lib/netstandard2.0/System.Collections.Immutable.dll", "lib/netstandard2.0/System.Collections.Immutable.xml", "lib/portable-net45+win8+wp8+wpa81/System.Collections.Immutable.dll", "lib/portable-net45+win8+wp8+wpa81/System.Collections.Immutable.xml", "system.collections.immutable.1.7.1.nupkg.sha512", "system.collections.immutable.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.ComponentModel.Annotations/4.7.0": {"sha512": "0YFqjhp/mYkDGpU0Ye1GjE53HMp9UVfGN7seGpAMttAC0C40v5gw598jCgpbBLMmCo0E5YRLBv5Z2doypO49ZQ==", "type": "package", "path": "system.componentmodel.annotations/4.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net461/System.ComponentModel.Annotations.dll", "lib/netcore50/System.ComponentModel.Annotations.dll", "lib/netstandard1.4/System.ComponentModel.Annotations.dll", "lib/netstandard2.0/System.ComponentModel.Annotations.dll", "lib/netstandard2.1/System.ComponentModel.Annotations.dll", "lib/netstandard2.1/System.ComponentModel.Annotations.xml", "lib/portable-net45+win8/_._", "lib/win8/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net461/System.ComponentModel.Annotations.dll", "ref/net461/System.ComponentModel.Annotations.xml", "ref/netcore50/System.ComponentModel.Annotations.dll", "ref/netcore50/System.ComponentModel.Annotations.xml", "ref/netcore50/de/System.ComponentModel.Annotations.xml", "ref/netcore50/es/System.ComponentModel.Annotations.xml", "ref/netcore50/fr/System.ComponentModel.Annotations.xml", "ref/netcore50/it/System.ComponentModel.Annotations.xml", "ref/netcore50/ja/System.ComponentModel.Annotations.xml", "ref/netcore50/ko/System.ComponentModel.Annotations.xml", "ref/netcore50/ru/System.ComponentModel.Annotations.xml", "ref/netcore50/zh-hans/System.ComponentModel.Annotations.xml", "ref/netcore50/zh-hant/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/System.ComponentModel.Annotations.dll", "ref/netstandard1.1/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/de/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/es/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/fr/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/it/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/ja/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/ko/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/ru/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/zh-hans/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/zh-hant/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/System.ComponentModel.Annotations.dll", "ref/netstandard1.3/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/de/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/es/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/fr/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/it/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/ja/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/ko/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/ru/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/zh-hans/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/zh-hant/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/System.ComponentModel.Annotations.dll", "ref/netstandard1.4/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/de/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/es/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/fr/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/it/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/ja/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/ko/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/ru/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/zh-hans/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/zh-hant/System.ComponentModel.Annotations.xml", "ref/netstandard2.0/System.ComponentModel.Annotations.dll", "ref/netstandard2.0/System.ComponentModel.Annotations.xml", "ref/netstandard2.1/System.ComponentModel.Annotations.dll", "ref/netstandard2.1/System.ComponentModel.Annotations.xml", "ref/portable-net45+win8/_._", "ref/win8/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.componentmodel.annotations.4.7.0.nupkg.sha512", "system.componentmodel.annotations.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Diagnostics.DiagnosticSource/4.7.1": {"sha512": "j81Lovt90PDAq8kLpaJfJKV/rWdWuEk6jfV+MBkee33vzYLEUsy4gXK8laa9V2nZlLM9VM9yA/OOQxxPEJKAMw==", "type": "package", "path": "system.diagnostics.diagnosticsource/4.7.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net45/System.Diagnostics.DiagnosticSource.dll", "lib/net45/System.Diagnostics.DiagnosticSource.xml", "lib/net46/System.Diagnostics.DiagnosticSource.dll", "lib/net46/System.Diagnostics.DiagnosticSource.xml", "lib/netstandard1.1/System.Diagnostics.DiagnosticSource.dll", "lib/netstandard1.1/System.Diagnostics.DiagnosticSource.xml", "lib/netstandard1.3/System.Diagnostics.DiagnosticSource.dll", "lib/netstandard1.3/System.Diagnostics.DiagnosticSource.xml", "lib/portable-net45+win8+wpa81/System.Diagnostics.DiagnosticSource.dll", "lib/portable-net45+win8+wpa81/System.Diagnostics.DiagnosticSource.xml", "system.diagnostics.diagnosticsource.4.7.1.nupkg.sha512", "system.diagnostics.diagnosticsource.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.IdentityModel.Tokens.Jwt/6.10.0": {"sha512": "C+Q5ORsFycRkRuvy/Xd0Pv5xVpmWSAvQYZAGs7VQogmkqlLhvfZXTgBIlHqC3cxkstSoLJAYx6xZB7foQ2y5eg==", "type": "package", "path": "system.identitymodel.tokens.jwt/6.10.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net45/System.IdentityModel.Tokens.Jwt.dll", "lib/net45/System.IdentityModel.Tokens.Jwt.xml", "lib/net461/System.IdentityModel.Tokens.Jwt.dll", "lib/net461/System.IdentityModel.Tokens.Jwt.xml", "lib/net472/System.IdentityModel.Tokens.Jwt.dll", "lib/net472/System.IdentityModel.Tokens.Jwt.xml", "lib/netstandard2.0/System.IdentityModel.Tokens.Jwt.dll", "lib/netstandard2.0/System.IdentityModel.Tokens.Jwt.xml", "system.identitymodel.tokens.jwt.6.10.0.nupkg.sha512", "system.identitymodel.tokens.jwt.nuspec"]}, "System.Reflection.Emit/4.7.0": {"sha512": "VR4kk8XLKebQ4MZuKuIni/7oh+QGFmZW3qORd1GvBq/8026OpW501SzT/oypwiQl4TvT8ErnReh/NzY9u+C6wQ==", "type": "package", "path": "system.reflection.emit/4.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Reflection.Emit.dll", "lib/netcoreapp2.0/_._", "lib/netstandard1.1/System.Reflection.Emit.dll", "lib/netstandard1.1/System.Reflection.Emit.xml", "lib/netstandard1.3/System.Reflection.Emit.dll", "lib/netstandard2.0/System.Reflection.Emit.dll", "lib/netstandard2.0/System.Reflection.Emit.xml", "lib/netstandard2.1/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcoreapp2.0/_._", "ref/netstandard1.1/System.Reflection.Emit.dll", "ref/netstandard1.1/System.Reflection.Emit.xml", "ref/netstandard1.1/de/System.Reflection.Emit.xml", "ref/netstandard1.1/es/System.Reflection.Emit.xml", "ref/netstandard1.1/fr/System.Reflection.Emit.xml", "ref/netstandard1.1/it/System.Reflection.Emit.xml", "ref/netstandard1.1/ja/System.Reflection.Emit.xml", "ref/netstandard1.1/ko/System.Reflection.Emit.xml", "ref/netstandard1.1/ru/System.Reflection.Emit.xml", "ref/netstandard1.1/zh-hans/System.Reflection.Emit.xml", "ref/netstandard1.1/zh-hant/System.Reflection.Emit.xml", "ref/netstandard2.0/System.Reflection.Emit.dll", "ref/netstandard2.0/System.Reflection.Emit.xml", "ref/netstandard2.1/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/System.Reflection.Emit.dll", "runtimes/aot/lib/netcore50/System.Reflection.Emit.xml", "system.reflection.emit.4.7.0.nupkg.sha512", "system.reflection.emit.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Security.Cryptography.Cng/4.5.0": {"sha512": "WG3r7EyjUe9CMPFSs6bty5doUqT+q9pbI80hlNzo2SkPkZ4VTuZkGWjpp77JB8+uaL4DFPRdBsAY+DX3dBK92A==", "type": "package", "path": "system.security.cryptography.cng/4.5.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Security.Cryptography.Cng.dll", "lib/net461/System.Security.Cryptography.Cng.dll", "lib/net462/System.Security.Cryptography.Cng.dll", "lib/net47/System.Security.Cryptography.Cng.dll", "lib/netcoreapp2.1/System.Security.Cryptography.Cng.dll", "lib/netstandard1.3/System.Security.Cryptography.Cng.dll", "lib/netstandard1.4/System.Security.Cryptography.Cng.dll", "lib/netstandard1.6/System.Security.Cryptography.Cng.dll", "lib/netstandard2.0/System.Security.Cryptography.Cng.dll", "lib/uap10.0.16299/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Security.Cryptography.Cng.dll", "ref/net461/System.Security.Cryptography.Cng.dll", "ref/net461/System.Security.Cryptography.Cng.xml", "ref/net462/System.Security.Cryptography.Cng.dll", "ref/net462/System.Security.Cryptography.Cng.xml", "ref/net47/System.Security.Cryptography.Cng.dll", "ref/net47/System.Security.Cryptography.Cng.xml", "ref/netcoreapp2.0/System.Security.Cryptography.Cng.dll", "ref/netcoreapp2.0/System.Security.Cryptography.Cng.xml", "ref/netcoreapp2.1/System.Security.Cryptography.Cng.dll", "ref/netcoreapp2.1/System.Security.Cryptography.Cng.xml", "ref/netstandard1.3/System.Security.Cryptography.Cng.dll", "ref/netstandard1.4/System.Security.Cryptography.Cng.dll", "ref/netstandard1.6/System.Security.Cryptography.Cng.dll", "ref/netstandard2.0/System.Security.Cryptography.Cng.dll", "ref/netstandard2.0/System.Security.Cryptography.Cng.xml", "ref/uap10.0.16299/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/win/lib/net46/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/net461/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/net462/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/net47/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/netcoreapp2.0/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/netcoreapp2.1/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/netstandard1.4/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/netstandard1.6/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/uap10.0.16299/_._", "system.security.cryptography.cng.4.5.0.nupkg.sha512", "system.security.cryptography.cng.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Text.Encodings.Web/4.5.0": {"sha512": "Xg4G4Indi4dqP1iuAiMSwpiWS54ZghzR644OtsRCm/m/lBMG8dUBhLVN7hLm8NNrNTR+iGbshCPTwrvxZPlm4g==", "type": "package", "path": "system.text.encodings.web/4.5.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/netstandard1.0/System.Text.Encodings.Web.dll", "lib/netstandard1.0/System.Text.Encodings.Web.xml", "lib/netstandard2.0/System.Text.Encodings.Web.dll", "lib/netstandard2.0/System.Text.Encodings.Web.xml", "system.text.encodings.web.4.5.0.nupkg.sha512", "system.text.encodings.web.nuspec", "useSharedDesignerContext.txt", "version.txt"]}}, "projectFileDependencyGroups": {".NETCoreApp,Version=v3.1": ["AutoMapper >= 10.1.1", "AutoMapper.Extensions.Microsoft.DependencyInjection >= 8.1.1", "BCrypt.Net-Next >= 4.0.3", "Microsoft.AspNetCore.Authentication.JwtBearer >= 3.1.32", "Microsoft.AspNetCore.Cors >= 2.2.0", "Microsoft.EntityFrameworkCore >= 3.1.32", "Microsoft.EntityFrameworkCore.Design >= 3.1.32", "Pomelo.EntityFrameworkCore.MySql >= 3.2.7", "Swashbuckle.AspNetCore >= 5.6.3", "System.IdentityModel.Tokens.Jwt >= 6.10.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\工作\\丰选\\丰选Git\\zyonline_api_test\\ZyOnlineApi.csproj", "projectName": "ZyOnlineApi", "projectPath": "D:\\工作\\丰选\\丰选Git\\zyonline_api_test\\ZyOnlineApi.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\工作\\丰选\\丰选Git\\zyonline_api_test\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netcoreapp3.1"], "sources": {"https://api.nuget.org/v3/index.json": {}, "https://nuget.org/api/v2/": {}}, "frameworks": {"netcoreapp3.1": {"targetAlias": "netcoreapp3.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netcoreapp3.1": {"targetAlias": "netcoreapp3.1", "dependencies": {"AutoMapper": {"target": "Package", "version": "[10.1.1, )"}, "AutoMapper.Extensions.Microsoft.DependencyInjection": {"target": "Package", "version": "[8.1.1, )"}, "BCrypt.Net-Next": {"target": "Package", "version": "[4.0.3, )"}, "Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[3.1.32, )"}, "Microsoft.AspNetCore.Cors": {"target": "Package", "version": "[2.2.0, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[3.1.32, )"}, "Microsoft.EntityFrameworkCore.Design": {"target": "Package", "version": "[3.1.32, )"}, "Pomelo.EntityFrameworkCore.MySql": {"target": "Package", "version": "[3.2.7, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[5.6.3, )"}, "System.IdentityModel.Tokens.Jwt": {"target": "Package", "version": "[6.10.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.403\\RuntimeIdentifierGraph.json"}}}}