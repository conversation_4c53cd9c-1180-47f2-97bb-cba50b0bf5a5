using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using AutoMapper;
using ZyOnlineApi.Data;
using ZyOnlineApi.DTOs;
using ZyOnlineApi.Models;

namespace ZyOnlineApi.Services
{
    public class CommonService : ICommonService
    {
        private readonly ApplicationDbContext _context;
        private readonly IMapper _mapper;

        public CommonService(ApplicationDbContext context, IMapper mapper)
        {
            _context = context;
            _mapper = mapper;
        }

        public async Task<List<ProjectDto>> GetActiveProjectsAsync()
        {
            var projects = await _context.Projects
                .Where(p => p.IsActive)
                .OrderBy(p => p.Name)
                .ToListAsync();

            return _mapper.Map<List<ProjectDto>>(projects);
        }

        public async Task<List<AvailableTimeSlotDto>> GetAvailableTimeSlotsAsync(DateTime date)
        {
            var timeSlots = await _context.AvailableTimeSlots
                .Where(ts => ts.Date.Date == date.Date)
                .OrderBy(ts => ts.StartTime)
                .ToListAsync();

            return _mapper.Map<List<AvailableTimeSlotDto>>(timeSlots);
        }

        public async Task<AvailableTimeSlotDto> CreateTimeSlotAsync(AvailableTimeSlotDto timeSlotDto)
        {
            // 检查时间段是否冲突
            var conflictingSlot = await _context.AvailableTimeSlots
                .FirstOrDefaultAsync(ts => ts.Date.Date == timeSlotDto.Date.Date &&
                                          ((ts.StartTime <= timeSlotDto.StartTime && ts.EndTime > timeSlotDto.StartTime) ||
                                           (ts.StartTime < timeSlotDto.EndTime && ts.EndTime >= timeSlotDto.EndTime) ||
                                           (ts.StartTime >= timeSlotDto.StartTime && ts.EndTime <= timeSlotDto.EndTime)));

            if (conflictingSlot != null)
            {
                throw new InvalidOperationException("时间段冲突");
            }

            var timeSlot = _mapper.Map<AvailableTimeSlot>(timeSlotDto);
            timeSlot.CreatedAt = DateTime.Now;
            timeSlot.UpdatedAt = DateTime.Now;

            _context.AvailableTimeSlots.Add(timeSlot);
            await _context.SaveChangesAsync();

            return _mapper.Map<AvailableTimeSlotDto>(timeSlot);
        }

        public async Task<AvailableTimeSlotDto> UpdateTimeSlotAsync(int id, AvailableTimeSlotDto timeSlotDto)
        {
            var timeSlot = await _context.AvailableTimeSlots.FindAsync(id);
            if (timeSlot == null)
            {
                throw new InvalidOperationException("时间段不存在");
            }

            // 检查时间段是否冲突（排除当前时间段）
            var conflictingSlot = await _context.AvailableTimeSlots
                .FirstOrDefaultAsync(ts => ts.Id != id &&
                                          ts.Date.Date == timeSlotDto.Date.Date &&
                                          ((ts.StartTime <= timeSlotDto.StartTime && ts.EndTime > timeSlotDto.StartTime) ||
                                           (ts.StartTime < timeSlotDto.EndTime && ts.EndTime >= timeSlotDto.EndTime) ||
                                           (ts.StartTime >= timeSlotDto.StartTime && ts.EndTime <= timeSlotDto.EndTime)));

            if (conflictingSlot != null)
            {
                throw new InvalidOperationException("时间段冲突");
            }

            _mapper.Map(timeSlotDto, timeSlot);
            timeSlot.UpdatedAt = DateTime.Now;

            await _context.SaveChangesAsync();

            return _mapper.Map<AvailableTimeSlotDto>(timeSlot);
        }

        public async Task<bool> DeleteTimeSlotAsync(int id)
        {
            var timeSlot = await _context.AvailableTimeSlots.FindAsync(id);
            if (timeSlot == null)
            {
                return false;
            }

            // 检查是否有预约使用该时间段
            var hasAppointments = await _context.Appointments
                .AnyAsync(a => a.AppointmentTime.Date == timeSlot.Date.Date &&
                              a.AppointmentTime.TimeOfDay >= timeSlot.StartTime &&
                              a.AppointmentTime.TimeOfDay < timeSlot.EndTime &&
                              a.Status != "已取消");

            if (hasAppointments)
            {
                throw new InvalidOperationException("该时间段已有预约，无法删除");
            }

            _context.AvailableTimeSlots.Remove(timeSlot);
            await _context.SaveChangesAsync();

            return true;
        }

        public async Task<List<InstructionDto>> GetActiveInstructionsAsync()
        {
            var instructions = await _context.Instructions
                .Where(i => i.IsActive)
                .OrderByDescending(i => i.CreatedAt)
                .ToListAsync();

            return _mapper.Map<List<InstructionDto>>(instructions);
        }

        public async Task<InstructionDto> GetInstructionByIdAsync(int id)
        {
            var instruction = await _context.Instructions.FindAsync(id);
            return _mapper.Map<InstructionDto>(instruction);
        }

        public async Task<InstructionDto> CreateInstructionAsync(InstructionDto instructionDto)
        {
            var instruction = _mapper.Map<Instruction>(instructionDto);
            instruction.CreatedAt = DateTime.Now;
            instruction.UpdatedAt = DateTime.Now;

            _context.Instructions.Add(instruction);
            await _context.SaveChangesAsync();

            return _mapper.Map<InstructionDto>(instruction);
        }

        public async Task<InstructionDto> UpdateInstructionAsync(int id, InstructionDto instructionDto)
        {
            var instruction = await _context.Instructions.FindAsync(id);
            if (instruction == null)
            {
                throw new InvalidOperationException("就诊须知不存在");
            }

            _mapper.Map(instructionDto, instruction);
            instruction.UpdatedAt = DateTime.Now;

            await _context.SaveChangesAsync();

            return _mapper.Map<InstructionDto>(instruction);
        }

        public async Task<bool> DeleteInstructionAsync(int id)
        {
            var instruction = await _context.Instructions.FindAsync(id);
            if (instruction == null)
            {
                return false;
            }

            instruction.IsActive = false;
            instruction.UpdatedAt = DateTime.Now;

            await _context.SaveChangesAsync();
            return true;
        }
    }
}
