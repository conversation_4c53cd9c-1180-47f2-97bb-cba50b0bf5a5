using Microsoft.EntityFrameworkCore;
using ZyOnlineApi.Models;

namespace ZyOnlineApi.Data
{
    public class ApplicationDbContext : DbContext
    {
        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options) : base(options)
        {
        }

        public DbSet<User> Users { get; set; }
        public DbSet<Patient> Patients { get; set; }
        public DbSet<Project> Projects { get; set; }
        public DbSet<Appointment> Appointments { get; set; }
        public DbSet<AvailableTimeSlot> AvailableTimeSlots { get; set; }
        public DbSet<Report> Reports { get; set; }
        public DbSet<Instruction> Instructions { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // 配置索引
            modelBuilder.Entity<Patient>()
                .HasIndex(p => p.WechatOpenId)
                .IsUnique();

            modelBuilder.Entity<Patient>()
                .HasIndex(p => p.IdCard);

            modelBuilder.Entity<Appointment>()
                .HasIndex(a => a.AppointmentTime);

            modelBuilder.Entity<Report>()
                .HasIndex(r => r.ReportId)
                .IsUnique();

            // 种子数据
            modelBuilder.Entity<Project>().HasData(
                new Project { Id = 1, Name = "PET", Description = "PET检查", IsActive = true },
                new Project { Id = 2, Name = "DR", Description = "DR检查", IsActive = true },
                new Project { Id = 3, Name = "CT", Description = "CT检查", IsActive = true }
            );

            modelBuilder.Entity<User>().HasData(
                new User 
                { 
                    Id = 1, 
                    Username = "admin", 
                    PasswordHash = BCrypt.Net.BCrypt.HashPassword("admin123"), 
                    Role = "admin", 
                    RealName = "系统管理员",
                    IsActive = true 
                }
            );
        }
    }
}
