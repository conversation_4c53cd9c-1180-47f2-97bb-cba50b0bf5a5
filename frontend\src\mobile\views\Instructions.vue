<template>
  <div class="instructions">
    <van-nav-bar title="就诊须知" left-arrow @click-left="$router.back()" />
    
    <div class="content">
      <div v-if="instructions.length" class="instruction-list">
        <div
          v-for="instruction in instructions"
          :key="instruction.id"
          class="instruction-card"
        >
          <h3 class="instruction-title">{{ instruction.title }}</h3>
          <div class="instruction-content" v-html="formatContent(instruction.content)"></div>
        </div>
      </div>

      <van-empty
        v-else
        image="search"
        description="暂无就诊须知"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { getInstructions } from '@/api/common'

const instructions = ref([])

const formatContent = (content) => {
  // 将换行符转换为HTML换行
  return content.replace(/\n/g, '<br>')
}

const loadInstructions = async () => {
  try {
    instructions.value = await getInstructions()
  } catch (error) {
    console.error('获取就诊须知失败:', error)
  }
}

onMounted(() => {
  loadInstructions()
})
</script>

<style scoped>
.instructions {
  background-color: #f7f8fa;
  min-height: 100vh;
}

.content {
  padding: 16px;
}

.instruction-list {
  space-y: 16px;
}

.instruction-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.instruction-title {
  font-size: 18px;
  font-weight: 600;
  color: #323233;
  margin: 0 0 16px 0;
  padding-bottom: 12px;
  border-bottom: 2px solid #1989fa;
}

.instruction-content {
  color: #646566;
  line-height: 1.6;
  font-size: 15px;
}

.instruction-content :deep(br) {
  margin-bottom: 8px;
}
</style>
