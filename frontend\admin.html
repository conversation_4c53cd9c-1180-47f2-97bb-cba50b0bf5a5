<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>智影在线管理系统</title>
    <!-- Vue 3 CDN -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <!-- Element Plus CDN -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css" />
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    <!-- ECharts CDN -->
    <script src="https://unpkg.com/echarts@5/dist/echarts.min.js"></script>
    <!-- Axios CDN -->
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
        background-color: #f5f5f5;
      }

      .login-container {
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 100vh;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      }

      .login-box {
        background: white;
        padding: 40px;
        border-radius: 10px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        width: 400px;
      }

      .login-box h2 {
        text-align: center;
        margin-bottom: 30px;
        color: #333;
      }

      .admin-layout {
        display: flex;
        flex-direction: column;
        height: 100vh;
      }

      .header {
        background: #409eff;
        color: white;
        padding: 0 20px;
        height: 60px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      }

      .header h1 {
        font-size: 20px;
        font-weight: 500;
      }

      .header-actions {
        display: flex;
        align-items: center;
        gap: 15px;
      }

      .admin-layout .el-container {
        flex: 1;
      }

      .sidebar {
        width: 200px;
        background: white;
        border-right: 1px solid #e6e6e6;
        height: calc(100vh - 60px);
        overflow-y: auto;
      }

      .main-content {
        flex: 1;
        padding: 20px;
        background: #f5f5f5;
        height: calc(100vh - 60px);
        overflow-y: auto;
      }

      .dashboard h2,
      .appointments h2,
      .patients h2,
      .reports h2,
      .settings h2 {
        margin-bottom: 20px;
        color: #333;
      }

      .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
      }

      .stat-card {
        background: white;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        text-align: center;
      }

      .stat-card h3 {
        color: #666;
        font-size: 14px;
        margin-bottom: 10px;
      }

      .stat-number {
        font-size: 32px;
        font-weight: bold;
        color: #409eff;
      }

      .admin-layout {
        display: flex;
        flex-direction: column;
      }

      .admin-layout .el-container {
        display: flex;
        flex: 1;
      }

      .sidebar {
        flex-shrink: 0;
      }

      .main-content {
        flex: 1;
      }
    </style>
  </head>
  <body>
    <div id="app"></div>
    <script src="/src/admin/main-cdn.js"></script>
  </body>
</html>
