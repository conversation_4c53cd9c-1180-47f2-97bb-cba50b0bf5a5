<template>
  <div class="dashboard">
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon today">
              <el-icon><Calendar /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.todayAppointments }}</div>
              <div class="stats-label">今日预约</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon pending">
              <el-icon><Clock /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.pendingAppointments }}</div>
              <div class="stats-label">待检查</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon completed">
              <el-icon><Check /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.completedAppointments }}</div>
              <div class="stats-label">已完成</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon total">
              <el-icon><User /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.totalPatients }}</div>
              <div class="stats-label">总就诊人</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20" class="charts-row">
      <el-col :span="12">
        <el-card title="预约趋势">
          <div class="chart-container">
            <div class="chart-placeholder">
              预约趋势图表
              <p>（可集成 ECharts 或其他图表库）</p>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="12">
        <el-card title="项目分布">
          <div class="chart-container">
            <div class="chart-placeholder">
              项目分布图表
              <p>（可集成 ECharts 或其他图表库）</p>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 最近预约 -->
    <el-card title="最近预约" class="recent-appointments">
      <el-table :data="recentAppointments" stripe>
        <el-table-column prop="patientName" label="就诊人" width="100" />
        <el-table-column prop="patientPhone" label="手机号" width="120" />
        <el-table-column prop="projectName" label="检查项目" width="100" />
        <el-table-column prop="appointmentTime" label="预约时间" width="160">
          <template #default="{ row }">
            {{ formatDateTime(row.appointmentTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createdAt" label="创建时间">
          <template #default="{ row }">
            {{ formatDateTime(row.createdAt) }}
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { getAppointments } from '@/api/appointment'
import { Calendar, Clock, Check, User } from '@element-plus/icons-vue'
import dayjs from 'dayjs'

const stats = reactive({
  todayAppointments: 0,
  pendingAppointments: 0,
  completedAppointments: 0,
  totalPatients: 0
})

const recentAppointments = ref([])

const formatDateTime = (dateTime) => {
  return dayjs(dateTime).format('YYYY-MM-DD HH:mm')
}

const getStatusType = (status) => {
  const typeMap = {
    '待检查': 'warning',
    '已完成': 'success',
    '已取消': 'danger'
  }
  return typeMap[status] || 'info'
}

const loadDashboardData = async () => {
  try {
    // 获取最近预约
    const result = await getAppointments({
      pageIndex: 1,
      pageSize: 10
    })
    recentAppointments.value = result.items

    // 计算统计数据
    const today = dayjs().format('YYYY-MM-DD')
    const allAppointments = await getAppointments({
      pageIndex: 1,
      pageSize: 1000 // 获取更多数据用于统计
    })

    stats.todayAppointments = allAppointments.items.filter(
      item => dayjs(item.appointmentTime).format('YYYY-MM-DD') === today
    ).length

    stats.pendingAppointments = allAppointments.items.filter(
      item => item.status === '待检查'
    ).length

    stats.completedAppointments = allAppointments.items.filter(
      item => item.status === '已完成'
    ).length

    // 统计不重复的就诊人数量
    const uniquePatients = new Set(allAppointments.items.map(item => item.patientId))
    stats.totalPatients = uniquePatients.size

  } catch (error) {
    console.error('加载仪表板数据失败:', error)
  }
}

onMounted(() => {
  loadDashboardData()
})
</script>

<style scoped>
.dashboard {
  padding: 0;
  background: transparent;
}

.stats-row {
  margin-bottom: 30px;
}

.stats-card {
  height: 140px;
  border-radius: 16px;
  border: none;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  overflow: hidden;
  position: relative;
}

.stats-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7));
  z-index: 1;
}

.stats-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.stats-card :deep(.el-card__body) {
  padding: 25px;
  height: 100%;
  position: relative;
  z-index: 2;
}

.stats-content {
  display: flex;
  align-items: center;
  height: 100%;
  position: relative;
}

.stats-icon {
  width: 70px;
  height: 70px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  font-size: 28px;
  color: white;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
  position: relative;
  overflow: hidden;
}

.stats-icon::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transform: rotate(45deg);
  animation: shimmer 3s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
  50% { transform: translateX(100%) translateY(100%) rotate(45deg); }
  100% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
}

.stats-icon.today {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stats-icon.pending {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stats-icon.completed {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stats-icon.total {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stats-info {
  flex: 1;
}

.stats-number {
  font-size: 36px;
  font-weight: 700;
  background: linear-gradient(135deg, #2c3e50, #3498db);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  line-height: 1;
  margin-bottom: 8px;
  animation: countUp 1s ease-out;
}

@keyframes countUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.stats-label {
  font-size: 16px;
  color: #7f8c8d;
  font-weight: 500;
  letter-spacing: 0.5px;
}

.charts-row {
  margin-bottom: 30px;
}

.charts-row :deep(.el-card) {
  border-radius: 16px;
  border: none;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.charts-row :deep(.el-card:hover) {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.12);
}

.charts-row :deep(.el-card__header) {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-bottom: 1px solid #e2e8f0;
  padding: 20px 25px;
  font-weight: 600;
  color: #2c3e50;
  font-size: 18px;
}

.charts-row :deep(.el-card__body) {
  padding: 25px;
}

.chart-container {
  height: 320px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-radius: 12px;
  border: 2px dashed #cbd5e0;
  position: relative;
  overflow: hidden;
}

.chart-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  animation: slideRight 3s infinite;
}

@keyframes slideRight {
  0% { left: -100%; }
  50% { left: 100%; }
  100% { left: -100%; }
}

.chart-placeholder {
  text-align: center;
  color: #64748b;
  font-size: 18px;
  font-weight: 500;
  z-index: 1;
  position: relative;
}

.chart-placeholder p {
  margin: 15px 0 0 0;
  font-size: 14px;
  color: #94a3b8;
  font-weight: 400;
}

.recent-appointments {
  margin-bottom: 30px;
}

.recent-appointments :deep(.el-card) {
  border-radius: 16px;
  border: none;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.recent-appointments :deep(.el-card:hover) {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.12);
}

.recent-appointments :deep(.el-card__header) {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-bottom: 1px solid #e2e8f0;
  padding: 20px 25px;
  font-weight: 600;
  color: #2c3e50;
  font-size: 18px;
}

.recent-appointments :deep(.el-card__body) {
  padding: 0;
}

.recent-appointments :deep(.el-table) {
  border-radius: 0 0 16px 16px;
  overflow: hidden;
}

.recent-appointments :deep(.el-table__header) {
  background: #f8fafc;
}

.recent-appointments :deep(.el-table th) {
  background: #f8fafc !important;
  color: #475569;
  font-weight: 600;
  border-bottom: 2px solid #e2e8f0;
}

.recent-appointments :deep(.el-table td) {
  border-bottom: 1px solid #f1f5f9;
  padding: 15px 12px;
}

.recent-appointments :deep(.el-table__row:hover) {
  background: #f8fafc !important;
}

.recent-appointments :deep(.el-tag) {
  border-radius: 20px;
  padding: 4px 12px;
  font-weight: 500;
  border: none;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .stats-row :deep(.el-col) {
    margin-bottom: 20px;
  }
}

@media (max-width: 768px) {
  .dashboard {
    padding: 0;
  }

  .stats-card {
    height: 120px;
  }

  .stats-icon {
    width: 60px;
    height: 60px;
    font-size: 24px;
  }

  .stats-number {
    font-size: 28px;
  }

  .stats-label {
    font-size: 14px;
  }

  .chart-container {
    height: 250px;
  }
}

/* 加载动画 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.stats-card {
  animation: fadeInUp 0.6s ease-out;
}

.stats-card:nth-child(1) { animation-delay: 0.1s; }
.stats-card:nth-child(2) { animation-delay: 0.2s; }
.stats-card:nth-child(3) { animation-delay: 0.3s; }
.stats-card:nth-child(4) { animation-delay: 0.4s; }

.charts-row :deep(.el-card) {
  animation: fadeInUp 0.6s ease-out 0.5s both;
}

.recent-appointments {
  animation: fadeInUp 0.6s ease-out 0.6s both;
}
</style>
