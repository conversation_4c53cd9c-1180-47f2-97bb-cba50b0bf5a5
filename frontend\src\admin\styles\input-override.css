/* 强制重置输入框高度 */
.el-input,
.el-input__wrapper,
.el-select,
.el-select .el-input,
.el-select .el-input__wrapper,
.el-date-editor,
.el-date-editor .el-input,
.el-date-editor .el-input__wrapper {
  height: 36px !important;
  min-height: 36px !important;
  max-height: 36px !important;
}

.el-input__wrapper,
.el-select .el-input__wrapper,
.el-date-editor .el-input__wrapper {
  padding: 8px 12px !important;
  border-radius: 10px !important;
  font-size: 14px !important;
}

.el-input__inner,
.el-select .el-input__inner,
.el-date-editor .el-input__inner {
  height: 20px !important;
  line-height: 20px !important;
  font-size: 14px !important;
}

/* 表单项间距调整 */
.el-form-item {
  margin-bottom: 16px !important;
}

.el-form--inline .el-form-item {
  margin-right: 16px !important;
  margin-bottom: 12px !important;
}

/* 标签样式 */
.el-form-item__label {
  font-size: 14px !important;
  line-height: 36px !important;
  height: 36px !important;
}
