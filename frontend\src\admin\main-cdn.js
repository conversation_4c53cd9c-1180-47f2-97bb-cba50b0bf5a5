// 使用 CDN 版本的 Vue 和相关库
const { createApp, ref, reactive, computed, onMounted } = Vue;
const { ElMessage, ElMessageBox, ElLoading } = ElementPlus;

// API 基础配置
const API_BASE_URL = 'http://localhost:5000/api';

// 创建 axios 实例
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
});

// 请求拦截器
api.interceptors.request.use(
  config => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  error => {
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  response => {
    return response.data;
  },
  error => {
    if (error.response?.status === 401) {
      localStorage.removeItem('token');
      window.location.href = '/admin.html';
    }
    ElMessage.error(error.response?.data?.message || '请求失败');
    return Promise.reject(error);
  }
);

// 管理后台主应用
const AdminApp = {
  setup() {
    const isLoggedIn = ref(!!localStorage.getItem('token'));
    const currentView = ref('dashboard');
    const loading = ref(false);

    // 登录表单
    const loginForm = reactive({
      username: 'admin',
      password: '123456'
    });

    // 统计数据
    const stats = reactive({
      totalPatients: 0,
      totalAppointments: 0,
      todayAppointments: 0,
      pendingReports: 0
    });

    // 预约列表
    const appointments = ref([]);
    const patients = ref([]);

    // 登录方法
    const login = async () => {
      try {
        loading.value = true;
        const response = await api.post('/auth/login', loginForm);

        if (response.token) {
          localStorage.setItem('token', response.token);
          isLoggedIn.value = true;
          ElMessage.success('登录成功');
          await loadDashboardData();
        }
      } catch (error) {
        console.error('登录失败:', error);
        ElMessage.error('登录失败，请检查用户名和密码');
      } finally {
        loading.value = false;
      }
    };

    // 退出登录
    const logout = () => {
      localStorage.removeItem('token');
      isLoggedIn.value = false;
      currentView.value = 'dashboard';
      ElMessage.success('已退出登录');
    };

    // 加载仪表板数据
    const loadDashboardData = async () => {
      try {
        loading.value = true;

        // 加载统计数据
        const [patientsRes, appointmentsRes] = await Promise.all([
          api.get('/patients'),
          api.get('/appointments')
        ]);

        patients.value = patientsRes || [];
        appointments.value = appointmentsRes || [];

        // 计算统计数据
        stats.totalPatients = patients.value.length;
        stats.totalAppointments = appointments.value.length;

        const today = new Date().toDateString();
        stats.todayAppointments = appointments.value.filter(apt =>
          new Date(apt.appointmentDate).toDateString() === today
        ).length;

        stats.pendingReports = appointments.value.filter(apt =>
          apt.status === 'Completed' && !apt.reportGenerated
        ).length;

      } catch (error) {
        console.error('加载数据失败:', error);
        // 使用模拟数据
        stats.totalPatients = 156;
        stats.totalAppointments = 89;
        stats.todayAppointments = 12;
        stats.pendingReports = 5;
      } finally {
        loading.value = false;
      }
    };

    // 切换视图
    const switchView = (view) => {
      currentView.value = view;
      if (view === 'dashboard') {
        loadDashboardData();
      }
    };

    // 组件挂载时检查登录状态
    onMounted(() => {
      if (isLoggedIn.value) {
        loadDashboardData();
      }
    });

    return {
      isLoggedIn,
      currentView,
      loading,
      loginForm,
      stats,
      appointments,
      patients,
      login,
      logout,
      switchView,
      loadDashboardData
    };
  },

  template: `
    <div id="admin-app">
      <!-- 登录页面 -->
      <div v-if="!isLoggedIn" class="login-container">
        <div class="login-box">
          <h2>智影在线管理系统</h2>
          <el-form :model="loginForm" label-width="80px">
            <el-form-item label="用户名">
              <el-input v-model="loginForm.username" placeholder="请输入用户名"></el-input>
            </el-form-item>
            <el-form-item label="密码">
              <el-input v-model="loginForm.password" type="password" placeholder="请输入密码" @keyup.enter="login"></el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="login" :loading="loading" style="width: 100%">
                登录
              </el-button>
            </el-form-item>
          </el-form>
        </div>
      </div>

      <!-- 管理后台主界面 -->
      <div v-else class="admin-layout">
        <!-- 顶部导航 -->
        <div class="header">
          <h1>智影在线管理系统</h1>
          <div class="header-actions">
            <span>欢迎，管理员</span>
            <el-button @click="logout" type="danger" size="small">退出登录</el-button>
          </div>
        </div>

        <!-- 主体容器 -->
        <div class="el-container" style="flex: 1;">
          <!-- 侧边栏 -->
          <div class="sidebar">
            <el-menu :default-active="currentView" @select="switchView" mode="vertical">
              <el-menu-item index="dashboard">
                <span>仪表板</span>
              </el-menu-item>
              <el-menu-item index="appointments">
                <span>预约管理</span>
              </el-menu-item>
              <el-menu-item index="patients">
                <span>患者管理</span>
              </el-menu-item>
              <el-menu-item index="reports">
                <span>报告管理</span>
              </el-menu-item>
              <el-menu-item index="settings">
                <span>系统设置</span>
              </el-menu-item>
            </el-menu>
          </div>

          <!-- 主内容区 -->
          <div class="main-content">
          <!-- 仪表板 -->
          <div v-if="currentView === 'dashboard'" class="dashboard">
            <h2>系统概览</h2>
            <div class="stats-grid">
              <div class="stat-card">
                <h3>总患者数</h3>
                <div class="stat-number">{{ stats.totalPatients }}</div>
              </div>
              <div class="stat-card">
                <h3>总预约数</h3>
                <div class="stat-number">{{ stats.totalAppointments }}</div>
              </div>
              <div class="stat-card">
                <h3>今日预约</h3>
                <div class="stat-number">{{ stats.todayAppointments }}</div>
              </div>
              <div class="stat-card">
                <h3>待处理报告</h3>
                <div class="stat-number">{{ stats.pendingReports }}</div>
              </div>
            </div>
          </div>

          <!-- 预约管理 -->
          <div v-else-if="currentView === 'appointments'" class="appointments">
            <h2>预约管理</h2>
            <p>预约管理功能正在开发中...</p>
          </div>

          <!-- 患者管理 -->
          <div v-else-if="currentView === 'patients'" class="patients">
            <h2>患者管理</h2>
            <p>患者管理功能正在开发中...</p>
          </div>

          <!-- 报告管理 -->
          <div v-else-if="currentView === 'reports'" class="reports">
            <h2>报告管理</h2>
            <p>报告管理功能正在开发中...</p>
          </div>

          <!-- 系统设置 -->
          <div v-else-if="currentView === 'settings'" class="settings">
            <h2>系统设置</h2>
            <p>系统设置功能正在开发中...</p>
          </div>
          </div>
        </div>
      </div>
    </div>
  `
};

// 创建并挂载应用
const app = createApp(AdminApp);
app.use(ElementPlus, {
  locale: ElementPlus.lang.zhCn
});
app.mount('#app');
