{"version": 3, "file": "index.min.js", "sources": ["../../src/composables/api.ts", "../../src/composables/autoresize.ts", "../../src/utils.ts", "../../src/composables/loading.ts", "../../src/wc.ts", "../../src/ECharts.ts", "../../src/global.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-explicit-any */\nimport type { Ref } from \"vue-demi\";\nimport type { EChartsType } from \"../types\";\n\nconst METHOD_NAMES = [\n  \"getWidth\",\n  \"getHeight\",\n  \"getDom\",\n  \"getOption\",\n  \"resize\",\n  \"dispatchAction\",\n  \"convertToPixel\",\n  \"convertFromPixel\",\n  \"containPixel\",\n  \"getDataURL\",\n  \"getConnectedDataURL\",\n  \"appendData\",\n  \"clear\",\n  \"isDisposed\",\n  \"dispose\"\n] as const;\n\ntype MethodName = (typeof METHOD_NAMES)[number];\n\ntype PublicMethods = Pick<EChartsType, MethodName>;\n\nexport function usePublicAPI(\n  chart: Ref<EChartsType | undefined>\n): PublicMethods {\n  function makePublicMethod<T extends MethodName>(\n    name: T\n  ): (...args: Parameters<EChartsType[T]>) => ReturnType<EChartsType[T]> {\n    return (...args) => {\n      if (!chart.value) {\n        throw new Error(\"ECharts is not initialized yet.\");\n      }\n      return (chart.value[name] as any).apply(chart.value, args);\n    };\n  }\n\n  function makePublicMethods(): PublicMethods {\n    const methods = Object.create(null);\n    METHOD_NAMES.forEach(name => {\n      methods[name] = makePublicMethod(name);\n    });\n\n    return methods as PublicMethods;\n  }\n\n  return makePublicMethods();\n}\n", "import { watch } from \"vue-demi\";\nimport { throttle } from \"echarts/core\";\n\nimport type { Ref, PropType } from \"vue-demi\";\nimport type { EChartsType, AutoResize } from \"../types\";\n\nexport function useAutoresize(\n  chart: Ref<EChartsType | undefined>,\n  autoresize: Ref<AutoResize | undefined>,\n  root: Ref<HTMLElement | undefined>\n): void {\n  watch(\n    [root, chart, autoresize],\n    ([root, chart, autoresize], _, onCleanup) => {\n      let ro: ResizeObserver | null = null;\n\n      if (root && chart && autoresize) {\n        const { offsetWidth, offsetHeight } = root;\n        const autoresizeOptions = autoresize === true ? {} : autoresize;\n        const { throttle: wait = 100, onResize } = autoresizeOptions;\n\n        let initialResizeTriggered = false;\n\n        const callback = () => {\n          chart.resize();\n          onResize?.();\n        };\n\n        const resizeCallback = wait ? throttle(callback, wait) : callback;\n\n        ro = new ResizeObserver(() => {\n          // We just skip ResizeObserver's initial resize callback if the\n          // size has not changed since the chart is rendered.\n          if (!initialResizeTriggered) {\n            initialResizeTriggered = true;\n            if (\n              root.offsetWidth === offsetWidth &&\n              root.offsetHeight === offsetHeight\n            ) {\n              return;\n            }\n          }\n          resizeCallback();\n        });\n        ro.observe(root);\n      }\n\n      onCleanup(() => {\n        if (ro) {\n          ro.disconnect();\n          ro = null;\n        }\n      });\n    }\n  );\n}\n\nexport const autoresizeProps = {\n  autoresize: [Boolean, Object] as PropType<AutoResize>\n};\n", "import { unref, isRef } from \"vue-demi\";\n\nimport type { Injection } from \"./types\";\n\ntype Attrs = {\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  [key: string]: any;\n};\n\n// Copied from\n// https://github.com/vuejs/vue-next/blob/5a7a1b8293822219283d6e267496bec02234b0bc/packages/shared/src/index.ts#L40-L41\nconst onRE = /^on[^a-z]/;\nexport const isOn = (key: string): boolean => onRE.test(key);\n\nexport function omitOn(attrs: Attrs): Attrs {\n  const result: Attrs = {};\n  for (const key in attrs) {\n    if (!isOn(key)) {\n      result[key] = attrs[key];\n    }\n  }\n\n  return result;\n}\n\nexport function unwrapInjected<T, V>(\n  injection: Injection<T>,\n  defaultValue: V\n): T | V {\n  const value = isRef(injection) ? unref(injection) : injection;\n\n  if (value && typeof value === \"object\" && \"value\" in value) {\n    return value.value || defaultValue;\n  }\n\n  return value || defaultValue;\n}\n", "import { unwrapInjected } from \"../utils\";\nimport { inject, computed, watchEffect } from \"vue-demi\";\n\nimport type { Ref, InjectionKey, PropType } from \"vue-demi\";\nimport type { EChartsType, LoadingOptions } from \"../types\";\n\nexport const LOADING_OPTIONS_KEY =\n  \"ecLoadingOptions\" as unknown as InjectionKey<\n    LoadingOptions | Ref<LoadingOptions>\n  >;\n\nexport function useLoading(\n  chart: Ref<EChartsType | undefined>,\n  loading: Ref<boolean>,\n  loadingOptions: Ref<LoadingOptions | undefined>\n): void {\n  const defaultLoadingOptions = inject(LOADING_OPTIONS_KEY, {});\n  const realLoadingOptions = computed(() => ({\n    ...unwrapInjected(defaultLoadingOptions, {}),\n    ...loadingOptions?.value\n  }));\n\n  watchEffect(() => {\n    const instance = chart.value;\n    if (!instance) {\n      return;\n    }\n\n    if (loading.value) {\n      instance.showLoading(realLoadingOptions.value);\n    } else {\n      instance.hideLoading();\n    }\n  });\n}\n\nexport const loadingProps = {\n  loading: Boolean,\n  loadingOptions: Object as PropType<LoadingOptions>\n};\n", "let registered: boolean | null = null;\n\nexport const TAG_NAME = \"x-vue-echarts\";\n\nexport interface EChartsElement extends HTMLElement {\n  __dispose: (() => void) | null;\n}\n\nexport function register(): boolean {\n  if (registered != null) {\n    return registered;\n  }\n\n  if (\n    typeof HTMLElement === \"undefined\" ||\n    typeof customElements === \"undefined\"\n  ) {\n    return (registered = false);\n  }\n\n  try {\n    // Class definitions cannot be transpiled to ES5\n    // so we are doing a little trick here to ensure\n    // we are using native classes. As we use this as\n    // a progressive enhancement, it will be fine even\n    // if the browser doesn't support native classes.\n    const reg = new Function(\n      \"tag\",\n      // Use esbuild repl to keep build process simple\n      // https://esbuild.github.io/try/#dAAwLjIzLjAALS1taW5pZnkAY2xhc3MgRUNoYXJ0c0VsZW1lbnQgZXh0ZW5kcyBIVE1MRWxlbWVudCB7CiAgX19kaXNwb3NlID0gbnVsbDsKCiAgZGlzY29ubmVjdGVkQ2FsbGJhY2soKSB7CiAgICBpZiAodGhpcy5fX2Rpc3Bvc2UpIHsKICAgICAgdGhpcy5fX2Rpc3Bvc2UoKTsKICAgICAgdGhpcy5fX2Rpc3Bvc2UgPSBudWxsOwogICAgfQogIH0KfQoKaWYgKGN1c3RvbUVsZW1lbnRzLmdldCh0YWcpID09IG51bGwpIHsKICBjdXN0b21FbGVtZW50cy5kZWZpbmUodGFnLCBFQ2hhcnRzRWxlbWVudCk7Cn0K\n      \"class EChartsElement extends HTMLElement{__dispose=null;disconnectedCallback(){this.__dispose&&(this.__dispose(),this.__dispose=null)}}customElements.get(tag)==null&&customElements.define(tag,EChartsElement);\"\n    );\n    reg(TAG_NAME);\n  } catch (e) {\n    return (registered = false);\n  }\n\n  return (registered = true);\n}\n", "/* eslint-disable vue/multi-word-component-names */\n/* eslint-disable @typescript-eslint/no-explicit-any */\nimport {\n  defineComponent,\n  shallowRef,\n  toRefs,\n  watch,\n  computed,\n  inject,\n  onMounted,\n  onBeforeUnmount,\n  h,\n  nextTick,\n  watchEffect,\n  getCurrentInstance,\n  Vue2\n} from \"vue-demi\";\nimport { init as initChart } from \"echarts/core\";\n\nimport {\n  usePublicAPI,\n  useAutoresize,\n  autoresizeProps,\n  useLoading,\n  loadingProps\n} from \"./composables\";\nimport { isOn, omitOn, unwrapInjected } from \"./utils\";\nimport { register, TAG_NAME } from \"./wc\";\n\nimport type { PropType, InjectionKey } from \"vue-demi\";\nimport type {\n  EChartsType,\n  EventTarget,\n  Option,\n  Theme,\n  ThemeInjection,\n  InitOptions,\n  InitOptionsInjection,\n  UpdateOptions,\n  UpdateOptionsInjection,\n  Emits\n} from \"./types\";\nimport type { EChartsElement } from \"./wc\";\n\nimport \"./style.css\";\n\nconst __CSP__ = false;\nconst wcRegistered = __CSP__ ? false : register();\n\nif (Vue2) {\n  Vue2.config.ignoredElements.push(TAG_NAME);\n}\n\nexport const THEME_KEY = \"ecTheme\" as unknown as InjectionKey<ThemeInjection>;\nexport const INIT_OPTIONS_KEY =\n  \"ecInitOptions\" as unknown as InjectionKey<InitOptionsInjection>;\nexport const UPDATE_OPTIONS_KEY =\n  \"ecUpdateOptions\" as unknown as InjectionKey<UpdateOptionsInjection>;\nexport { LOADING_OPTIONS_KEY } from \"./composables\";\n\nconst NATIVE_EVENT_RE = /(^&?~?!?)native:/;\n\nexport default defineComponent({\n  name: \"echarts\",\n  props: {\n    option: Object as PropType<Option>,\n    theme: {\n      type: [Object, String] as PropType<Theme>\n    },\n    initOptions: Object as PropType<InitOptions>,\n    updateOptions: Object as PropType<UpdateOptions>,\n    group: String,\n    manualUpdate: Boolean,\n    ...autoresizeProps,\n    ...loadingProps\n  },\n  emits: {} as unknown as Emits,\n  inheritAttrs: false,\n  setup(props, { attrs }) {\n    const root = shallowRef<EChartsElement>();\n    const chart = shallowRef<EChartsType>();\n    const manualOption = shallowRef<Option>();\n    const defaultTheme = inject(THEME_KEY, null);\n    const defaultInitOptions = inject(INIT_OPTIONS_KEY, null);\n    const defaultUpdateOptions = inject(UPDATE_OPTIONS_KEY, null);\n\n    const { autoresize, manualUpdate, loading, loadingOptions } = toRefs(props);\n\n    const realOption = computed(\n      () => manualOption.value || props.option || null\n    );\n    const realTheme = computed(\n      () => props.theme || unwrapInjected(defaultTheme, {})\n    );\n    const realInitOptions = computed(\n      () => props.initOptions || unwrapInjected(defaultInitOptions, {})\n    );\n    const realUpdateOptions = computed(\n      () => props.updateOptions || unwrapInjected(defaultUpdateOptions, {})\n    );\n    const nonEventAttrs = computed(() => omitOn(attrs));\n    const nativeListeners: Record<string, unknown> = {};\n\n    // @ts-expect-error listeners for Vue 2 compatibility\n    const listeners = getCurrentInstance().proxy.$listeners;\n    const realListeners: Record<string, any> = {};\n\n    if (!listeners) {\n      // This is for Vue 3.\n      // We are converting all `on<Event>` props to event listeners compatible with Vue 2\n      // and collect them into `realListeners` so that we can bind them to the chart instance\n      // later in the same way.\n      // For `onNative:<event>` props, we just strip the `Native:` part and collect them into\n      // `nativeListeners` so that we can bind them to the root element directly.\n      Object.keys(attrs)\n        .filter(key => isOn(key))\n        .forEach(key => {\n          // onClick    -> c + lick\n          // onZr:click -> z + r:click\n          let event = key.charAt(2).toLowerCase() + key.slice(3);\n\n          // Collect native DOM events\n          if (event.indexOf(\"native:\") === 0) {\n            // native:click -> onClick\n            const nativeKey = `on${event.charAt(7).toUpperCase()}${event.slice(\n              8\n            )}`;\n\n            nativeListeners[nativeKey] = attrs[key];\n            return;\n          }\n\n          // clickOnce    -> ~click\n          // zr:clickOnce -> ~zr:click\n          if (event.substring(event.length - 4) === \"Once\") {\n            event = `~${event.substring(0, event.length - 4)}`;\n          }\n\n          realListeners[event] = attrs[key];\n        });\n    } else {\n      // This is for Vue 2.\n      // We just need to distinguish normal events and `native:<event>` events and\n      // collect them into `realListeners` and `nativeListeners` respectively.\n      // For `native:<event>` events, we just strip the `native:` part and collect them\n      // into `nativeListeners` so that we can bind them to the root element directly.\n      // native:click   -> click\n      // ~native:click  -> ~click\n      // &~!native:click -> &~!click\n      Object.keys(listeners).forEach(key => {\n        if (NATIVE_EVENT_RE.test(key)) {\n          nativeListeners[key.replace(NATIVE_EVENT_RE, \"$1\")] = listeners[key];\n        } else {\n          realListeners[key] = listeners[key];\n        }\n      });\n    }\n\n    function init(option?: Option) {\n      if (!root.value) {\n        return;\n      }\n\n      const instance = (chart.value = initChart(\n        root.value,\n        realTheme.value,\n        realInitOptions.value\n      ));\n\n      if (props.group) {\n        instance.group = props.group;\n      }\n\n      Object.keys(realListeners).forEach(key => {\n        let handler = realListeners[key];\n\n        if (!handler) {\n          return;\n        }\n\n        let event = key.toLowerCase();\n        if (event.charAt(0) === \"~\") {\n          event = event.substring(1);\n          handler.__once__ = true;\n        }\n\n        let target: EventTarget = instance;\n        if (event.indexOf(\"zr:\") === 0) {\n          target = instance.getZr();\n          event = event.substring(3);\n        }\n\n        if (handler.__once__) {\n          delete handler.__once__;\n\n          const raw = handler;\n\n          handler = (...args: any[]) => {\n            raw(...args);\n            target.off(event, handler);\n          };\n        }\n\n        // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n        // @ts-ignore EChartsType[\"on\"] is not compatible with ZRenderType[\"on\"]\n        // but it's okay here\n        target.on(event, handler);\n      });\n\n      function resize() {\n        if (instance && !instance.isDisposed()) {\n          instance.resize();\n        }\n      }\n\n      function commit() {\n        const opt = option || realOption.value;\n        if (opt) {\n          instance.setOption(opt, realUpdateOptions.value);\n        }\n      }\n\n      if (autoresize.value) {\n        // Try to make chart fit to container in case container size\n        // is changed synchronously or in already queued microtasks\n        nextTick(() => {\n          resize();\n          commit();\n        });\n      } else {\n        commit();\n      }\n    }\n\n    function setOption(option: Option, updateOptions?: UpdateOptions) {\n      if (props.manualUpdate) {\n        manualOption.value = option;\n      }\n\n      if (!chart.value) {\n        init(option);\n      } else {\n        chart.value.setOption(option, updateOptions || {});\n      }\n    }\n\n    function cleanup() {\n      if (chart.value) {\n        chart.value.dispose();\n        chart.value = undefined;\n      }\n    }\n\n    let unwatchOption: (() => void) | null = null;\n    watch(\n      manualUpdate,\n      manualUpdate => {\n        if (typeof unwatchOption === \"function\") {\n          unwatchOption();\n          unwatchOption = null;\n        }\n\n        if (!manualUpdate) {\n          unwatchOption = watch(\n            () => props.option,\n            (option, oldOption) => {\n              if (!option) {\n                return;\n              }\n              if (!chart.value) {\n                init();\n              } else {\n                chart.value.setOption(option, {\n                  // mutating `option` will lead to `notMerge: false` and\n                  // replacing it with new reference will lead to `notMerge: true`\n                  notMerge: option !== oldOption,\n                  ...realUpdateOptions.value\n                });\n              }\n            },\n            { deep: true }\n          );\n        }\n      },\n      {\n        immediate: true\n      }\n    );\n\n    watch(\n      [realTheme, realInitOptions],\n      () => {\n        cleanup();\n        init();\n      },\n      {\n        deep: true\n      }\n    );\n\n    watchEffect(() => {\n      if (props.group && chart.value) {\n        chart.value.group = props.group;\n      }\n    });\n\n    const publicApi = usePublicAPI(chart);\n\n    useLoading(chart, loading, loadingOptions);\n\n    useAutoresize(chart, autoresize, root);\n\n    onMounted(() => {\n      init();\n    });\n\n    onBeforeUnmount(() => {\n      if (wcRegistered && root.value) {\n        // For registered web component, we can leverage the\n        // `disconnectedCallback` to dispose the chart instance\n        // so that we can delay the cleanup after exsiting leaving\n        // transition.\n        root.value.__dispose = cleanup;\n      } else {\n        cleanup();\n      }\n    });\n\n    return {\n      chart,\n      root,\n      setOption,\n      nonEventAttrs,\n      nativeListeners,\n      ...publicApi\n    };\n  },\n  render() {\n    // Vue 3 and Vue 2 have different vnode props format:\n    // See https://v3-migration.vuejs.org/breaking-changes/render-function-api.html#vnode-props-format\n    const attrs = (\n      Vue2\n        ? { attrs: this.nonEventAttrs, on: this.nativeListeners }\n        : { ...this.nonEventAttrs, ...this.nativeListeners }\n    ) as any;\n    attrs.ref = \"root\";\n    attrs.class = attrs.class ? [\"echarts\"].concat(attrs.class) : \"echarts\";\n    return h(TAG_NAME, attrs);\n  }\n});\n", "import \"echarts\";\nimport ECharts, * as exported from \"./index\";\n\nexport default {\n  ...ECharts,\n  ...exported\n};\n"], "names": ["METHOD_NAMES", "usePublicAPI", "chart", "makePublicMethod", "name", "args", "makePublicMethods", "methods", "useAutoresize", "autoresize", "root", "watch", "_", "onCleanup", "ro", "offsetWidth", "offsetHeight", "autoresizeOptions", "wait", "onResize", "initialResizeTriggered", "callback", "resizeCallback", "throttle", "autoresizeProps", "onRE", "isOn", "key", "omitOn", "attrs", "result", "unwrapInjected", "injection", "defaultValue", "value", "isRef", "unref", "LOADING_OPTIONS_KEY", "useLoading", "loading", "loadingOptions", "defaultLoadingOptions", "inject", "realLoadingOptions", "computed", "watchEffect", "instance", "loadingProps", "TAG_NAME", "Vue2", "THEME_KEY", "INIT_OPTIONS_KEY", "UPDATE_OPTIONS_KEY", "NATIVE_EVENT_RE", "defineComponent", "props", "shallowRef", "manualOption", "defaultTheme", "defaultInitOptions", "defaultUpdateOptions", "manualUpdate", "toRefs", "realOption", "realTheme", "realInitOptions", "realUpdateOptions", "nonEventAttrs", "nativeListeners", "listeners", "getCurrentInstance", "realListeners", "event", "<PERSON><PERSON><PERSON>", "init", "option", "initChart", "handler", "target", "raw", "resize", "commit", "opt", "nextTick", "setOption", "updateOptions", "cleanup", "unwatchOption", "oldOption", "publicApi", "onMounted", "onBeforeUnmount", "h", "global", "<PERSON><PERSON><PERSON>", "exported"], "mappings": "o9DAIA,MAAMA,EAAe,CACnB,WACA,YACA,SACA,YACA,SACA,iBACA,iBACA,mBACA,eACA,aACA,sBACA,aACA,QACA,aACA,SACF,EAMgB,SAAAC,EACdC,EACe,CACf,SAASC,EACPC,EACqE,CACrE,MAAO,IAAIC,IAAS,CAClB,GAAI,CAACH,EAAM,MACT,MAAM,IAAI,MAAM,iCAAiC,EAEnD,OAAQA,EAAM,MAAME,CAAI,EAAU,MAAMF,EAAM,MAAOG,CAAI,CAC3D,CACF,CAEA,SAASC,GAAmC,CAC1C,MAAMC,EAAU,OAAO,OAAO,IAAI,EAClC,OAAAP,EAAa,QAAQI,GAAQ,CAC3BG,EAAQH,CAAI,EAAID,EAAiBC,CAAI,CACvC,CAAC,EAEMG,CACT,CAEA,OAAOD,EACT,CAAA,CC5CgB,SAAAE,EACdN,EACAO,EACAC,EACM,CACNC,EAAAA,MACE,CAACD,EAAMR,EAAOO,CAAU,EACxB,CAAC,CAACC,EAAMR,EAAOO,CAAU,EAAGG,EAAGC,IAAc,CAC3C,IAAIC,EAA4B,KAEhC,GAAIJ,GAAQR,GAASO,EAAY,CAC/B,KAAM,CAAE,YAAAM,EAAa,aAAAC,CAAa,EAAIN,EAChCO,EAAoBR,IAAe,GAAO,CAAA,EAAKA,EAC/C,CAAE,SAAUS,EAAO,IAAK,SAAAC,CAAS,EAAIF,EAE3C,IAAIG,EAAyB,GAE7B,MAAMC,EAAW,IAAM,CACrBnB,EAAM,OACNiB,EAAAA,IACF,CAAA,EAEMG,EAAiBJ,EAAOK,EAAAA,SAASF,EAAUH,CAAI,EAAIG,EAEzDP,EAAK,IAAI,eAAe,IAAM,CAGxB,CAACM,IACHA,EAAyB,GAEvBV,EAAK,cAAgBK,GACrBL,EAAK,eAAiBM,IAK1BM,EACF,CAAA,CAAC,EACDR,EAAG,QAAQJ,CAAI,CACjB,CAEAG,EAAU,IAAM,CACVC,IACFA,EAAG,aACHA,EAAK,KAET,CAAC,CACH,CACF,CACF,CAEO,MAAMU,EAAkB,CAC7B,WAAY,CAAC,QAAS,MAAM,CAC9B,EChDMC,EAAO,YACAC,EAAQC,GAAyBF,EAAK,KAAKE,CAAG,EAEpD,SAASC,EAAOC,EAAqB,CAC1C,MAAMC,EAAgB,GACtB,UAAWH,KAAOE,EACXH,EAAKC,CAAG,IACXG,EAAOH,CAAG,EAAIE,EAAMF,CAAG,GAI3B,OAAOG,CACT,CAEO,SAASC,EACdC,EACAC,EACO,CACP,MAAMC,EAAQC,QAAMH,CAAS,EAAII,EAAMJ,MAAAA,CAAS,EAAIA,EAEpD,OAAIE,GAAS,OAAOA,GAAU,UAAY,UAAWA,EAC5CA,EAAM,OAASD,EAGjBC,GAASD,CAClB,CC9Ba,MAAAI,EACX,mBAIc,SAAAC,EACdpC,EACAqC,EACAC,EACM,CACN,MAAMC,EAAwBC,EAAAA,OAAOL,EAAqB,EAAE,EACtDM,EAAqBC,EAAAA,SAAS,KAAO,CACzC,GAAGb,EAAeU,EAAuB,CAAA,CAAE,EAC3C,GAAGD,GAAgB,KACrB,EAAE,EAEFK,EAAAA,YAAY,IAAM,CAChB,MAAMC,EAAW5C,EAAM,MAClB4C,IAIDP,EAAQ,MACVO,EAAS,YAAYH,EAAmB,KAAK,EAE7CG,EAAS,cAEb,CAAC,CACH,OAEaC,EAAe,CAC1B,QAAS,QACT,eAAgB,MAClB,ECrCaC,EAAW,gBC+CpBC,EAAAA,MACFA,EAAAA,KAAK,OAAO,gBAAgB,KAAKD,CAAQ,QAG9BE,EAAY,UACZC,EACX,gBACWC,EACX,kBAGIC,EAAkB,mBAExB,IAAeC,EAAAA,EAAAA,gBAAgB,CAC7B,KAAM,UACN,MAAO,CACL,OAAQ,OACR,MAAO,CACL,KAAM,CAAC,OAAQ,MAAM,CACvB,EACA,YAAa,OACb,cAAe,OACf,MAAO,OACP,aAAc,QACd,GAAG9B,EACH,GAAGuB,CACL,EACA,MAAO,CAAA,EACP,aAAc,GACd,MAAMQ,EAAO,CAAE,MAAA1B,CAAM,EAAG,CACtB,MAAMnB,EAAO8C,EAAAA,aACPtD,EAAQsD,EAAAA,WAAAA,EACRC,EAAeD,EAAAA,WACfE,EAAAA,EAAehB,SAAOQ,EAAW,IAAI,EACrCS,EAAqBjB,EAAAA,OAAOS,EAAkB,IAAI,EAClDS,EAAuBlB,EAAAA,OAAOU,EAAoB,IAAI,EAEtD,CAAE,WAAA3C,EAAY,aAAAoD,EAAc,QAAAtB,EAAS,eAAAC,CAAe,EAAIsB,EAAAA,OAAOP,CAAK,EAEpEQ,EAAanB,EAAAA,SACjB,IAAMa,EAAa,OAASF,EAAM,QAAU,IAC9C,EACMS,EAAYpB,EAAAA,SAChB,IAAMW,EAAM,OAASxB,EAAe2B,EAAc,CAAA,CAAE,CACtD,EACMO,EAAkBrB,EAAAA,SACtB,IAAMW,EAAM,aAAexB,EAAe4B,EAAoB,CAAE,CAAA,CAClE,EACMO,EAAoBtB,EACxB,SAAA,IAAMW,EAAM,eAAiBxB,EAAe6B,EAAsB,CAAE,CAAA,CACtE,EACMO,EAAgBvB,EAAS,SAAA,IAAMhB,EAAOC,CAAK,CAAC,EAC5CuC,EAA2C,CAAA,EAG3CC,EAAYC,EAAAA,mBAAqB,EAAA,MAAM,WACvCC,EAAqC,CAAA,EAEtCF,EA0CH,OAAO,KAAKA,CAAS,EAAE,QAAQ1C,GAAO,CAChC0B,EAAgB,KAAK1B,CAAG,EAC1ByC,EAAgBzC,EAAI,QAAQ0B,EAAiB,IAAI,CAAC,EAAIgB,EAAU1C,CAAG,EAEnE4C,EAAc5C,CAAG,EAAI0C,EAAU1C,CAAG,CAEtC,CAAC,EAzCD,OAAO,KAAKE,CAAK,EACd,OAAOF,GAAOD,EAAKC,CAAG,CAAC,EACvB,QAAQA,GAAO,CAGd,IAAI6C,EAAQ7C,EAAI,OAAO,CAAC,EAAE,YAAY,EAAIA,EAAI,MAAM,CAAC,EAGrD,GAAI6C,EAAM,QAAQ,SAAS,IAAM,EAAG,CAElC,MAAMC,EAAY,KAAKD,EAAM,OAAO,CAAC,EAAE,YAAa,CAAA,GAAGA,EAAM,MAC3D,CACF,CAAC,GAEDJ,EAAgBK,CAAS,EAAI5C,EAAMF,CAAG,EACtC,MACF,CAII6C,EAAM,UAAUA,EAAM,OAAS,CAAC,IAAM,SACxCA,EAAQ,IAAIA,EAAM,UAAU,EAAGA,EAAM,OAAS,CAAC,CAAC,IAGlDD,EAAcC,CAAK,EAAI3C,EAAMF,CAAG,CAClC,CAAC,EAmBL,SAAS+C,EAAKC,EAAiB,CAC7B,GAAI,CAACjE,EAAK,MACR,OAGF,MAAMoC,EAAY5C,EAAM,MAAQ0E,EAAAA,KAC9BlE,EAAK,MACLsD,EAAU,MACVC,EAAgB,KAClB,EAEIV,EAAM,QACRT,EAAS,MAAQS,EAAM,OAGzB,OAAO,KAAKgB,CAAa,EAAE,QAAQ5C,GAAO,CACxC,IAAIkD,EAAUN,EAAc5C,CAAG,EAE/B,GAAI,CAACkD,EACH,OAGF,IAAIL,EAAQ7C,EAAI,YAAY,EACxB6C,EAAM,OAAO,CAAC,IAAM,MACtBA,EAAQA,EAAM,UAAU,CAAC,EACzBK,EAAQ,SAAW,IAGrB,IAAIC,EAAsBhC,EAM1B,GALI0B,EAAM,QAAQ,KAAK,IAAM,IAC3BM,EAAShC,EAAS,MAClB0B,EAAAA,EAAQA,EAAM,UAAU,CAAC,GAGvBK,EAAQ,SAAU,CACpB,OAAOA,EAAQ,SAEf,MAAME,GAAMF,EAEZA,EAAU,IAAIxE,KAAgB,CAC5B0E,GAAI,GAAG1E,EAAI,EACXyE,EAAO,IAAIN,EAAOK,CAAO,CAC3B,CACF,CAKAC,EAAO,GAAGN,EAAOK,CAAO,CAC1B,CAAC,EAED,SAASG,GAAS,CACZlC,GAAY,CAACA,EAAS,WAAA,GACxBA,EAAS,QAEb,CAEA,SAASmC,GAAS,CAChB,MAAMC,EAAMP,GAAUZ,EAAW,MAC7BmB,GACFpC,EAAS,UAAUoC,EAAKhB,EAAkB,KAAK,CAEnD,CAEIzD,EAAW,MAGb0E,EAAAA,SAAS,IAAM,CACbH,IACAC,EAAO,CACT,CAAC,EAEDA,EAEJ,CAAA,CAEA,SAASG,EAAUT,EAAgBU,EAA+B,CAC5D9B,EAAM,eACRE,EAAa,MAAQkB,GAGlBzE,EAAM,MAGTA,EAAM,MAAM,UAAUyE,EAAQU,GAAiB,CAAE,CAAA,EAFjDX,EAAKC,CAAM,CAIf,CAEA,SAASW,GAAU,CACbpF,EAAM,QACRA,EAAM,MAAM,QAAQ,EACpBA,EAAM,MAAQ,OAElB,CAEA,IAAIqF,EAAqC,KACzC5E,EAAAA,MACEkD,EACAA,GAAgB,CACV,OAAO0B,GAAkB,aAC3BA,EACAA,EAAAA,EAAgB,MAGb1B,IACH0B,EAAgB5E,EACd,MAAA,IAAM4C,EAAM,OACZ,CAACoB,EAAQa,IAAc,CAChBb,IAGAzE,EAAM,MAGTA,EAAM,MAAM,UAAUyE,EAAQ,CAG5B,SAAUA,IAAWa,EACrB,GAAGtB,EAAkB,KACvB,CAAC,EAPDQ,EASJ,EAAA,EACA,CAAE,KAAM,EAAK,CACf,EAEJ,EACA,CACE,UAAW,EACb,CACF,EAEA/D,EAAAA,MACE,CAACqD,EAAWC,CAAe,EAC3B,IAAM,CACJqB,IACAZ,EAAK,CACP,EACA,CACE,KAAM,EACR,CACF,EAEA7B,cAAY,IAAM,CACZU,EAAM,OAASrD,EAAM,QACvBA,EAAM,MAAM,MAAQqD,EAAM,MAE9B,CAAC,EAED,MAAMkC,GAAYxF,EAAaC,CAAK,EAEpC,OAAAoC,EAAWpC,EAAOqC,EAASC,CAAc,EAEzChC,EAAcN,EAAOO,EAAYC,CAAI,EAErCgF,YAAU,IAAM,CACdhB,EAAK,CACP,CAAC,EAEDiB,EAAAA,gBAAgB,IAAM,CAQlBL,GAEJ,CAAC,EAEM,CACL,MAAApF,EACA,KAAAQ,EACA,UAAA0E,EACA,cAAAjB,EACA,gBAAAC,EACA,GAAGqB,EACL,CACF,EACA,QAAS,CAGP,MAAM5D,EACJoB,EAAAA,KACI,CAAE,MAAO,KAAK,cAAe,GAAI,KAAK,eAAgB,EACtD,CAAE,GAAG,KAAK,cAAe,GAAG,KAAK,eAAgB,EAEvD,OAAApB,EAAM,IAAM,OACZA,EAAM,MAAQA,EAAM,MAAQ,CAAC,SAAS,EAAE,OAAOA,EAAM,KAAK,EAAI,UACvD+D,EAAAA,EAAE5C,EAAUnB,CAAK,CAC1B,CACF,CAAC,wHC1VDgE,EAAe,CACb,GAAGC,EACH,GAAGC,CACL"}