<template>
  <div class="appointments">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>预约管理</span>
          <el-button type="primary" @click="syncReports">
            <el-icon><Refresh /></el-icon>
            同步报告
          </el-button>
        </div>
      </template>

      <!-- 搜索筛选 -->
      <div class="search-form">
        <el-form :model="searchForm" inline>
          <el-form-item label="状态">
            <el-select v-model="searchForm.status" placeholder="全部状态" clearable>
              <el-option label="全部" value="" />
              <el-option label="待检查" value="待检查" />
              <el-option label="已完成" value="已完成" />
              <el-option label="已取消" value="已取消" />
            </el-select>
          </el-form-item>

          <el-form-item label="就诊人姓名">
            <el-input
              v-model="searchForm.patientName"
              placeholder="请输入就诊人姓名"
              clearable
            />
          </el-form-item>

          <el-form-item label="手机号">
            <el-input
              v-model="searchForm.patientPhone"
              placeholder="请输入手机号"
              clearable
            />
          </el-form-item>

          <el-form-item label="预约日期">
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>

          <el-form-item>
            <el-button type="primary" @click="handleSearch">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="handleReset">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 数据表格 -->
      <el-table
        v-loading="loading"
        :data="appointments"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="id" label="预约ID" width="80" />
        <el-table-column prop="patientName" label="就诊人" width="100" />
        <el-table-column prop="patientPhone" label="手机号" width="120" />
        <el-table-column prop="projectName" label="检查项目" width="100" />
        <el-table-column prop="appointmentTime" label="预约时间" width="160">
          <template #default="{ row }">
            {{ formatDateTime(row.appointmentTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="remarks" label="备注" show-overflow-tooltip />
        <el-table-column prop="createdAt" label="创建时间" width="160">
          <template #default="{ row }">
            {{ formatDateTime(row.createdAt) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button
              v-if="row.status === '待检查'"
              type="success"
              size="small"
              @click="updateStatus(row, '已完成')"
            >
              完成
            </el-button>
            <el-button
              v-if="row.status === '待检查'"
              type="danger"
              size="small"
              @click="updateStatus(row, '已取消')"
            >
              取消
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="pagination.pageIndex"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          :prev-text="'上一页'"
          :next-text="'下一页'"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch } from 'vue'
import { getAppointments, updateAppointmentStatus } from '@/api/appointment'
import { syncReports } from '@/api/report'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Refresh } from '@element-plus/icons-vue'
import dayjs from 'dayjs'

const loading = ref(false)
const appointments = ref([])
const dateRange = ref([])

const searchForm = reactive({
  status: '',
  patientName: '',
  patientPhone: '',
  startDate: '',
  endDate: ''
})

const pagination = reactive({
  pageIndex: 1,
  pageSize: 10,
  total: 0
})

const formatDateTime = (dateTime) => {
  return dayjs(dateTime).format('YYYY-MM-DD HH:mm')
}

const getStatusType = (status) => {
  const typeMap = {
    '待检查': 'warning',
    '已完成': 'success',
    '已取消': 'danger'
  }
  return typeMap[status] || 'info'
}

const loadAppointments = async () => {
  loading.value = true
  try {
    const params = {
      ...searchForm,
      pageIndex: pagination.pageIndex,
      pageSize: pagination.pageSize
    }

    if (dateRange.value && dateRange.value.length === 2) {
      params.startDate = dateRange.value[0]
      params.endDate = dateRange.value[1]
    }

    const result = await getAppointments(params)
    appointments.value = result.items
    pagination.total = result.totalCount
  } catch (error) {
    console.error('获取预约列表失败:', error)
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  pagination.pageIndex = 1
  loadAppointments()
}

const handleReset = () => {
  Object.assign(searchForm, {
    status: '',
    patientName: '',
    patientPhone: '',
    startDate: '',
    endDate: ''
  })
  dateRange.value = []
  pagination.pageIndex = 1
  loadAppointments()
}

const handleSizeChange = (size) => {
  pagination.pageSize = size
  pagination.pageIndex = 1
  loadAppointments()
}

const handleCurrentChange = (page) => {
  pagination.pageIndex = page
  loadAppointments()
}

const updateStatus = async (appointment, status) => {
  try {
    const action = status === '已完成' ? '完成' : '取消'
    await ElMessageBox.confirm(`确定要${action}这个预约吗？`, '确认操作', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await updateAppointmentStatus(appointment.id, { status })
    ElMessage.success(`${action}成功`)
    loadAppointments()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('更新状态失败:', error)
    }
  }
}

const handleSyncReports = async () => {
  try {
    await ElMessageBox.confirm('确定要同步联影报告吗？', '确认操作', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'info'
    })

    await syncReports()
    ElMessage.success('报告同步成功')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('同步报告失败:', error)
    }
  }
}

watch(dateRange, (newVal) => {
  if (newVal && newVal.length === 2) {
    searchForm.startDate = newVal[0]
    searchForm.endDate = newVal[1]
  } else {
    searchForm.startDate = ''
    searchForm.endDate = ''
  }
})

onMounted(() => {
  loadAppointments()
})
</script>

<style scoped>
.appointments {
  height: 100%;
  padding: 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 15px;
}

.card-header span {
  font-size: 20px;
  font-weight: 600;
  background: linear-gradient(135deg, #2c3e50, #3498db);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.card-header .el-button {
  border-radius: 12px;
  font-weight: 500;
  padding: 10px 20px;
  transition: all 0.3s ease;
}

.card-header .el-button--primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.card-header .el-button--primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.card-header .el-button--info {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  border: none;
  box-shadow: 0 4px 12px rgba(79, 172, 254, 0.3);
}

.card-header .el-button--info:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(79, 172, 254, 0.4);
}

.search-form {
  margin-bottom: 25px;
  padding: 25px;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-radius: 16px;
  border: 1px solid #e2e8f0;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.search-form:hover {
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
}

.search-form :deep(.el-form-item) {
  margin-bottom: 16px;
}

.search-form :deep(.el-form-item__label) {
  font-weight: 500;
  color: #475569;
  font-size: 14px;
  line-height: 36px;
}

.search-form :deep(.el-input__wrapper) {
  border-radius: 10px;
  border: 1px solid #d1d5db;
  transition: all 0.3s ease;
  min-height: 36px;
  padding: 8px 12px;
}

.search-form :deep(.el-input__wrapper:hover) {
  border-color: #667eea;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

.search-form :deep(.el-input__wrapper.is-focus) {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.search-form :deep(.el-select) {
  width: 100%;
}

.search-form :deep(.el-date-editor) {
  width: 100%;
}

.search-form :deep(.el-button) {
  border-radius: 10px;
  font-weight: 500;
  padding: 10px 20px;
  transition: all 0.3s ease;
}

.search-form :deep(.el-button--primary) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.search-form :deep(.el-button--primary:hover) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.search-form :deep(.el-button--default) {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  color: #64748b;
}

.search-form :deep(.el-button--default:hover) {
  background: #e2e8f0;
  border-color: #cbd5e0;
  transform: translateY(-1px);
}

.pagination {
  margin-top: 25px;
  text-align: right;
  padding: 20px 0;
}

.pagination :deep(.el-pagination) {
  justify-content: flex-end;
}

.pagination :deep(.el-pager li) {
  border-radius: 8px;
  margin: 0 4px;
  transition: all 0.3s ease;
  font-weight: 500;
}

.pagination :deep(.el-pager li:hover) {
  background: #667eea;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.pagination :deep(.el-pager li.is-active) {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.pagination :deep(.btn-prev),
.pagination :deep(.btn-next) {
  border-radius: 8px;
  transition: all 0.3s ease;
}

.pagination :deep(.btn-prev:hover),
.pagination :deep(.btn-next:hover) {
  background: #667eea;
  color: white;
  transform: translateY(-2px);
}

/* 表格样式增强 */
.appointments :deep(.el-table) {
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
  border: none;
}

.appointments :deep(.el-table__header) {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.appointments :deep(.el-table th) {
  background: transparent !important;
  color: #475569;
  font-weight: 600;
  border-bottom: 2px solid #e2e8f0;
  padding: 15px 12px;
  font-size: 14px;
}

.appointments :deep(.el-table td) {
  border-bottom: 1px solid #f1f5f9;
  padding: 15px 12px;
  font-size: 14px;
}

.appointments :deep(.el-table__row) {
  transition: all 0.3s ease;
}

.appointments :deep(.el-table__row:hover) {
  background: #f8fafc !important;
  transform: scale(1.01);
}

.appointments :deep(.el-button--small) {
  border-radius: 8px;
  font-weight: 500;
  padding: 6px 12px;
  margin: 0 2px;
  transition: all 0.3s ease;
}

.appointments :deep(.el-button--success) {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  border: none;
  box-shadow: 0 2px 8px rgba(67, 233, 123, 0.3);
}

.appointments :deep(.el-button--success:hover) {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(67, 233, 123, 0.4);
}

.appointments :deep(.el-button--danger) {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
  border: none;
  box-shadow: 0 2px 8px rgba(255, 107, 107, 0.3);
}

.appointments :deep(.el-button--danger:hover) {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(255, 107, 107, 0.4);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .card-header {
    flex-direction: column;
    align-items: stretch;
  }

  .card-header .el-button {
    width: 100%;
    margin-top: 10px;
  }

  .search-form {
    padding: 20px;
  }

  .pagination {
    text-align: center;
  }

  .appointments :deep(.el-table__row:hover) {
    transform: none;
  }
}

/* 加载动画 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.appointments {
  animation: fadeInUp 0.6s ease-out;
}
</style>
