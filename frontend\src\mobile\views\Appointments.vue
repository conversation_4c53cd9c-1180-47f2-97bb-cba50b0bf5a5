<template>
  <div class="appointments">
    <van-nav-bar title="我的预约" left-arrow @click-left="$router.back()" />
    
    <div class="content">
      <!-- 预约列表 -->
      <div v-if="appointments.length" class="appointment-list">
        <div
          v-for="appointment in appointments"
          :key="appointment.id"
          class="appointment-card"
        >
          <div class="appointment-header">
            <div class="patient-info">
              <span class="patient-name">{{ appointment.patientName }}</span>
              <span class="project-name">{{ appointment.projectName }}</span>
            </div>
            <van-tag :type="getStatusType(appointment.status)">
              {{ appointment.status }}
            </van-tag>
          </div>
          
          <div class="appointment-time">
            <van-icon name="clock-o" />
            {{ formatDateTime(appointment.appointmentTime) }}
          </div>
          
          <div class="appointment-phone">
            <van-icon name="phone-o" />
            {{ appointment.patientPhone }}
          </div>
          
          <div v-if="appointment.remarks" class="appointment-remarks">
            <van-icon name="comment-o" />
            {{ appointment.remarks }}
          </div>
          
          <div class="appointment-actions" v-if="appointment.status === '待检查'">
            <van-button
              size="small"
              type="danger"
              @click="cancelAppointment(appointment)"
            >
              取消预约
            </van-button>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <van-empty
        v-else
        image="search"
        description="暂无预约记录"
      >
        <van-button
          round
          type="primary"
          class="bottom-button"
          @click="$router.push('/appointment')"
        >
          立即预约
        </van-button>
      </van-empty>
    </div>

    <!-- 取消预约确认弹窗 -->
    <van-dialog
      v-model:show="showCancelDialog"
      title="确认取消"
      :message="`确定要取消"${selectedAppointment?.patientName}"的预约吗？`"
      show-cancel-button
      @confirm="confirmCancel"
    />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { getMyAppointments, cancelAppointment as cancelAppointmentApi } from '@/api/appointment'
import { showToast } from 'vant'
import dayjs from 'dayjs'

const router = useRouter()

const appointments = ref([])
const showCancelDialog = ref(false)
const selectedAppointment = ref(null)

const formatDateTime = (dateTime) => {
  return dayjs(dateTime).format('MM月DD日 HH:mm')
}

const getStatusType = (status) => {
  const typeMap = {
    '待检查': 'warning',
    '已完成': 'success',
    '已取消': 'danger'
  }
  return typeMap[status] || 'default'
}

const loadAppointments = async () => {
  try {
    appointments.value = await getMyAppointments()
  } catch (error) {
    console.error('获取预约列表失败:', error)
  }
}

const cancelAppointment = (appointment) => {
  selectedAppointment.value = appointment
  showCancelDialog.value = true
}

const confirmCancel = async () => {
  try {
    await cancelAppointmentApi(selectedAppointment.value.id)
    showToast.success('取消成功')
    loadAppointments()
  } catch (error) {
    console.error('取消失败:', error)
  }
  showCancelDialog.value = false
  selectedAppointment.value = null
}

onMounted(() => {
  loadAppointments()
})
</script>

<style scoped>
.appointments {
  background-color: #f7f8fa;
  min-height: 100vh;
}

.content {
  padding: 16px;
}

.appointment-list {
  space-y: 12px;
}

.appointment-card {
  background: white;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.appointment-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.patient-info {
  flex: 1;
}

.patient-name {
  font-size: 18px;
  font-weight: 600;
  color: #323233;
  display: block;
  margin-bottom: 4px;
}

.project-name {
  font-size: 14px;
  color: #1989fa;
  background: #e8f4ff;
  padding: 2px 8px;
  border-radius: 4px;
}

.appointment-time,
.appointment-phone,
.appointment-remarks {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  color: #646566;
  font-size: 14px;
}

.appointment-time .van-icon,
.appointment-phone .van-icon,
.appointment-remarks .van-icon {
  margin-right: 8px;
  color: #969799;
}

.appointment-actions {
  margin-top: 12px;
  text-align: right;
}

.bottom-button {
  margin-top: 20px;
}
</style>
