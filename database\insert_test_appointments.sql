-- 插入测试预约数据
-- 首先确保有患者数据
INSERT IGNORE INTO patients (name, phone, id_card, gender, birth_date, wechat_open_id, created_at, updated_at) VALUES
('张三', '13800138001', '110101199001011234', '男', '1990-01-01', 'wx_openid_001', NOW(), NOW()),
('李四', '13800138002', '110101199002022345', '女', '1990-02-02', 'wx_openid_002', NOW(), NOW()),
('王五', '13800138003', '110101199003033456', '男', '1990-03-03', 'wx_openid_003', NOW(), NOW()),
('赵六', '13800138004', '110101199004044567', '女', '1990-04-04', 'wx_openid_004', NOW(), NOW()),
('钱七', '13800138005', '110101199005055678', '男', '1990-05-05', 'wx_openid_005', NOW(), NOW()),
('孙八', '13800138006', '110101199006066789', '女', '1990-06-06', 'wx_openid_006', NOW(), NOW()),
('周九', '13800138007', '110101199007077890', '男', '1990-07-07', 'wx_openid_007', NOW(), NOW()),
('吴十', '13800138008', '110101199008088901', '女', '1990-08-08', 'wx_openid_008', NOW(), NOW()),
('郑十一', '13800138009', '110101199009099012', '男', '1990-09-09', 'wx_openid_009', NOW(), NOW()),
('王十二', '13800138010', '110101199010101123', '女', '1990-10-10', 'wx_openid_010', NOW(), NOW());

-- 插入预约数据
INSERT INTO appointments (patient_id, project_id, appointment_time, status, remarks, created_at, updated_at) VALUES
(1, 1, '2024-12-20 09:00:00', '待检查', 'PET检查预约', NOW(), NOW()),
(2, 2, '2024-12-20 10:00:00', '待检查', 'DR检查预约', NOW(), NOW()),
(3, 3, '2024-12-20 11:00:00', '已完成', 'CT检查已完成', NOW(), NOW()),
(4, 1, '2024-12-21 09:00:00', '待检查', 'PET检查预约', NOW(), NOW()),
(5, 2, '2024-12-21 10:00:00', '已取消', '患者临时有事取消', NOW(), NOW()),
(6, 3, '2024-12-21 11:00:00', '待检查', 'CT检查预约', NOW(), NOW()),
(7, 1, '2024-12-22 09:00:00', '已完成', 'PET检查已完成', NOW(), NOW()),
(8, 2, '2024-12-22 10:00:00', '待检查', 'DR检查预约', NOW(), NOW()),
(9, 3, '2024-12-22 11:00:00', '待检查', 'CT检查预约', NOW(), NOW()),
(10, 1, '2024-12-23 09:00:00', '待检查', 'PET检查预约', NOW(), NOW());
