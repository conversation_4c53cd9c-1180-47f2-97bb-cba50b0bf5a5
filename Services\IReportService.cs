using System.Collections.Generic;
using System.Threading.Tasks;
using ZyOnlineApi.DTOs;

namespace ZyOnlineApi.Services
{
    public interface IReportService
    {
        Task<List<ReportDto>> GetReportsByWechatOpenIdAsync(string wechatOpenId);
        Task<ReportDto> GetReportByIdAsync(int id, string wechatOpenId);
        Task SyncReportsFromLianYingAsync();
        Task<List<ReportDto>> GetReportsByPatientIdAsync(int patientId, string wechatOpenId);
    }
}
