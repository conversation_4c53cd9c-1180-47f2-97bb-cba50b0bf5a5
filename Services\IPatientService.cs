using System.Collections.Generic;
using System.Threading.Tasks;
using ZyOnlineApi.DTOs;

namespace ZyOnlineApi.Services
{
    public interface IPatientService
    {
        Task<List<PatientDto>> GetPatientsByWechatOpenIdAsync(string wechatOpenId);
        Task<PatientDto> GetPatientByIdAsync(int id);
        Task<PatientDto> CreatePatientAsync(CreatePatientDto createPatientDto, string wechatOpenId);
        Task<PatientDto> UpdatePatientAsync(int id, UpdatePatientDto updatePatientDto, string wechatOpenId);
        Task<bool> DeletePatientAsync(int id, string wechatOpenId);
        Task<bool> ValidatePatientOwnershipAsync(int patientId, string wechatOpenId);
    }
}
