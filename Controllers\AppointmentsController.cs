using System;
using System.Collections.Generic;
using System.Security.Claims;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ZyOnlineApi.DTOs;
using ZyOnlineApi.Services;

namespace ZyOnlineApi.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class AppointmentsController : ControllerBase
    {
        private readonly IAppointmentService _appointmentService;

        public AppointmentsController(IAppointmentService appointmentService)
        {
            _appointmentService = appointmentService;
        }

        /// <summary>
        /// 获取当前用户的预约列表（微信端）
        /// </summary>
        [HttpGet("my")]
        [Authorize(Roles = "wechat_user")]
        public async Task<ActionResult<ApiResponse<List<AppointmentDto>>>> GetMyAppointments()
        {
            try
            {
                var wechatOpenId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                var appointments = await _appointmentService.GetAppointmentsByWechatOpenIdAsync(wechatOpenId);
                return Ok(ApiResponse<List<AppointmentDto>>.SuccessResult(appointments));
            }
            catch (Exception ex)
            {
                return BadRequest(ApiResponse<List<AppointmentDto>>.ErrorResult($"获取预约列表失败: {ex.Message}"));
            }
        }

        /// <summary>
        /// 创建预约（微信端）
        /// </summary>
        [HttpPost]
        [Authorize(Roles = "wechat_user")]
        public async Task<ActionResult<ApiResponse<AppointmentDto>>> CreateAppointment([FromBody] CreateAppointmentDto createAppointmentDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ApiResponse<AppointmentDto>.ErrorResult("输入数据验证失败"));
                }

                var wechatOpenId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                var appointment = await _appointmentService.CreateAppointmentAsync(createAppointmentDto, wechatOpenId);
                
                return CreatedAtAction(nameof(GetAppointment), new { id = appointment.Id }, 
                    ApiResponse<AppointmentDto>.SuccessResult(appointment, "预约创建成功"));
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(ApiResponse<AppointmentDto>.ErrorResult(ex.Message));
            }
            catch (Exception ex)
            {
                return BadRequest(ApiResponse<AppointmentDto>.ErrorResult($"创建预约失败: {ex.Message}"));
            }
        }

        /// <summary>
        /// 取消预约（微信端）
        /// </summary>
        [HttpPut("{id}/cancel")]
        [Authorize(Roles = "wechat_user")]
        public async Task<ActionResult<ApiResponse<bool>>> CancelAppointment(int id)
        {
            try
            {
                var wechatOpenId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                var result = await _appointmentService.CancelAppointmentAsync(id, wechatOpenId);
                
                if (!result)
                {
                    return NotFound(ApiResponse<bool>.ErrorResult("预约不存在或无法取消", 404));
                }

                return Ok(ApiResponse<bool>.SuccessResult(true, "预约取消成功"));
            }
            catch (Exception ex)
            {
                return BadRequest(ApiResponse<bool>.ErrorResult($"取消预约失败: {ex.Message}"));
            }
        }

        /// <summary>
        /// 获取预约详情
        /// </summary>
        [HttpGet("{id}")]
        [Authorize]
        public async Task<ActionResult<ApiResponse<AppointmentDto>>> GetAppointment(int id)
        {
            try
            {
                var appointment = await _appointmentService.GetAppointmentByIdAsync(id);
                if (appointment == null)
                {
                    return NotFound(ApiResponse<AppointmentDto>.ErrorResult("预约不存在", 404));
                }

                // 权限验证
                var role = User.FindFirst(ClaimTypes.Role)?.Value;
                if (role == "wechat_user")
                {
                    var wechatOpenId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                    // 这里需要验证预约是否属于当前微信用户，暂时简化处理
                }

                return Ok(ApiResponse<AppointmentDto>.SuccessResult(appointment));
            }
            catch (Exception ex)
            {
                return BadRequest(ApiResponse<AppointmentDto>.ErrorResult($"获取预约详情失败: {ex.Message}"));
            }
        }

        /// <summary>
        /// 获取预约列表（后台管理端）
        /// </summary>
        [HttpGet]
        [Authorize(Roles = "admin,staff")]
        public async Task<ActionResult<ApiResponse<PagedResult<AppointmentDto>>>> GetAppointments([FromQuery] AppointmentQueryDto query)
        {
            try
            {
                var result = await _appointmentService.GetAppointmentsAsync(query);
                return Ok(ApiResponse<PagedResult<AppointmentDto>>.SuccessResult(result));
            }
            catch (Exception ex)
            {
                return BadRequest(ApiResponse<PagedResult<AppointmentDto>>.ErrorResult($"获取预约列表失败: {ex.Message}"));
            }
        }

        /// <summary>
        /// 更新预约状态（后台管理端）
        /// </summary>
        [HttpPut("{id}/status")]
        [Authorize(Roles = "admin,staff")]
        public async Task<ActionResult<ApiResponse<AppointmentDto>>> UpdateAppointmentStatus(int id, [FromBody] UpdateAppointmentStatusDto updateStatusDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ApiResponse<AppointmentDto>.ErrorResult("输入数据验证失败"));
                }

                var appointment = await _appointmentService.UpdateAppointmentStatusAsync(id, updateStatusDto);
                return Ok(ApiResponse<AppointmentDto>.SuccessResult(appointment, "预约状态更新成功"));
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(ApiResponse<AppointmentDto>.ErrorResult(ex.Message));
            }
            catch (Exception ex)
            {
                return BadRequest(ApiResponse<AppointmentDto>.ErrorResult($"更新预约状态失败: {ex.Message}"));
            }
        }

        /// <summary>
        /// 获取可预约时间段
        /// </summary>
        [HttpGet("available-slots")]
        [Authorize]
        public async Task<ActionResult<ApiResponse<List<AvailableTimeSlotDto>>>> GetAvailableTimeSlots([FromQuery] DateTime date)
        {
            try
            {
                var timeSlots = await _appointmentService.GetAvailableTimeSlotsAsync(date);
                return Ok(ApiResponse<List<AvailableTimeSlotDto>>.SuccessResult(timeSlots));
            }
            catch (Exception ex)
            {
                return BadRequest(ApiResponse<List<AvailableTimeSlotDto>>.ErrorResult($"获取可预约时间段失败: {ex.Message}"));
            }
        }
    }
}
