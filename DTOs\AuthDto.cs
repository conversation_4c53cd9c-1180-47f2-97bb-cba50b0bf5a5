using System;
using System.ComponentModel.DataAnnotations;

namespace ZyOnlineApi.DTOs
{
    public class LoginDto
    {
        [Required(ErrorMessage = "用户名不能为空")]
        public string Username { get; set; }

        [Required(ErrorMessage = "密码不能为空")]
        public string Password { get; set; }
    }

    public class LoginResponseDto
    {
        public string Token { get; set; }
        public string Username { get; set; }
        public string RealName { get; set; }
        public string Role { get; set; }
        public DateTime ExpiresAt { get; set; }
    }

    public class WechatLoginDto
    {
        [Required(ErrorMessage = "微信OpenId不能为空")]
        public string OpenId { get; set; }
    }
}
