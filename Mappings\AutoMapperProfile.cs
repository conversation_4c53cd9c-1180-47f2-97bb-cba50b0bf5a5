using AutoMapper;
using ZyOnlineApi.DTOs;
using ZyOnlineApi.Models;

namespace ZyOnlineApi.Mappings
{
    public class AutoMapperProfile : Profile
    {
        public AutoMapperProfile()
        {
            // Patient mappings
            CreateMap<Patient, PatientDto>();
            CreateMap<CreatePatientDto, Patient>();
            CreateMap<UpdatePatientDto, Patient>();

            // Appointment mappings
            CreateMap<Appointment, AppointmentDto>()
                .ForMember(dest => dest.PatientName, opt => opt.MapFrom(src => src.Patient.Name))
                .ForMember(dest => dest.PatientPhone, opt => opt.MapFrom(src => src.Patient.Phone))
                .ForMember(dest => dest.ProjectName, opt => opt.MapFrom(src => src.Project.Name));
            CreateMap<CreateAppointmentDto, Appointment>();

            // Project mappings
            CreateMap<Project, ProjectDto>();

            // AvailableTimeSlot mappings
            CreateMap<AvailableTimeSlot, AvailableTimeSlotDto>();
            CreateMap<AvailableTimeSlotDto, AvailableTimeSlot>()
                .ForMember(dest => dest.Id, opt => opt.Ignore())
                .ForMember(dest => dest.CreatedAt, opt => opt.Ignore())
                .ForMember(dest => dest.UpdatedAt, opt => opt.Ignore());

            // Report mappings
            CreateMap<Report, ReportDto>();

            // Instruction mappings
            CreateMap<Instruction, InstructionDto>();
            CreateMap<InstructionDto, Instruction>()
                .ForMember(dest => dest.Id, opt => opt.Ignore())
                .ForMember(dest => dest.CreatedAt, opt => opt.Ignore())
                .ForMember(dest => dest.UpdatedAt, opt => opt.Ignore());
        }
    }
}
