import { createRouter, createWebHashHistory } from 'vue-router'
import { useUserStore } from '@/stores/user'

const routes = [
  {
    path: '/',
    redirect: '/home'
  },
  {
    path: '/home',
    name: 'Home',
    component: () => import('@/mobile/views/Home.vue'),
    meta: { title: '首页', requiresAuth: true }
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/mobile/views/Login.vue'),
    meta: { title: '登录' }
  },
  {
    path: '/patients',
    name: 'Patients',
    component: () => import('@/mobile/views/Patients.vue'),
    meta: { title: '就诊人管理', requiresAuth: true }
  },
  {
    path: '/patients/add',
    name: 'AddPatient',
    component: () => import('@/mobile/views/AddPatient.vue'),
    meta: { title: '添加就诊人', requiresAuth: true }
  },
  {
    path: '/patients/edit/:id',
    name: 'EditPatient',
    component: () => import('@/mobile/views/EditPatient.vue'),
    meta: { title: '编辑就诊人', requiresAuth: true }
  },
  {
    path: '/appointment',
    name: 'Appointment',
    component: () => import('@/mobile/views/Appointment.vue'),
    meta: { title: '在线预约', requiresAuth: true }
  },
  {
    path: '/appointments',
    name: 'Appointments',
    component: () => import('@/mobile/views/Appointments.vue'),
    meta: { title: '我的预约', requiresAuth: true }
  },
  {
    path: '/reports',
    name: 'Reports',
    component: () => import('@/mobile/views/Reports.vue'),
    meta: { title: '我的报告', requiresAuth: true }
  },
  {
    path: '/instructions',
    name: 'Instructions',
    component: () => import('@/mobile/views/Instructions.vue'),
    meta: { title: '就诊须知' }
  }
]

const router = createRouter({
  history: createWebHashHistory(),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const userStore = useUserStore()

  // 设置页面标题
  document.title = to.meta.title || '智影在线服务'

  // 检查是否需要登录
  if (to.meta.requiresAuth && !userStore.token) {
    next('/login')
  } else {
    next()
  }
})

export default router
