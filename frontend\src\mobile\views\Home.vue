<template>
  <div class="home">
    <!-- 顶部轮播图 -->
    <van-swipe class="banner" :autoplay="3000" indicator-color="white">
      <van-swipe-item>
        <div class="banner-item banner1">
          <h2>智影在线服务</h2>
          <p>便捷预约，智能服务</p>
        </div>
      </van-swipe-item>
      <van-swipe-item>
        <div class="banner-item banner2">
          <h2>专业影像检查</h2>
          <p>PET、DR、CT全覆盖</p>
        </div>
      </van-swipe-item>
    </van-swipe>

    <!-- 功能菜单 -->
    <div class="menu-grid">
      <div class="menu-item" @click="$router.push('/patients')">
        <van-icon name="friends" />
        <span>就诊人管理</span>
      </div>
      <div class="menu-item" @click="$router.push('/appointment')">
        <van-icon name="calendar-o" />
        <span>在线预约</span>
      </div>
      <div class="menu-item" @click="$router.push('/appointments')">
        <van-icon name="orders-o" />
        <span>我的预约</span>
      </div>
      <div class="menu-item" @click="$router.push('/reports')">
        <van-icon name="description" />
        <span>我的报告</span>
      </div>
    </div>

    <!-- 快捷操作 -->
    <van-cell-group title="快捷操作" inset>
      <van-cell
        title="添加就诊人"
        icon="add-o"
        is-link
        @click="$router.push('/patients/add')"
      />
      <van-cell
        title="立即预约"
        icon="calendar-o"
        is-link
        @click="$router.push('/appointment')"
      />
      <van-cell
        title="就诊须知"
        icon="info-o"
        is-link
        @click="$router.push('/instructions')"
      />
    </van-cell-group>

    <!-- 最近预约 -->
    <van-cell-group title="最近预约" inset v-if="recentAppointments.length">
      <van-cell
        v-for="appointment in recentAppointments"
        :key="appointment.id"
        :title="appointment.patientName"
        :label="`${appointment.projectName} | ${formatDate(appointment.appointmentTime)}`"
        :value="appointment.status"
        is-link
        @click="$router.push('/appointments')"
      />
    </van-cell-group>

    <!-- 底部导航 -->
    <van-tabbar v-model="activeTab" @change="onTabChange">
      <van-tabbar-item icon="home-o" to="/home">首页</van-tabbar-item>
      <van-tabbar-item icon="calendar-o" to="/appointments">预约</van-tabbar-item>
      <van-tabbar-item icon="description" to="/reports">报告</van-tabbar-item>
      <van-tabbar-item icon="user-o" @click="showUserMenu = true">我的</van-tabbar-item>
    </van-tabbar>

    <!-- 用户菜单 -->
    <van-action-sheet
      v-model:show="showUserMenu"
      :actions="userActions"
      @select="onUserAction"
    />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { getMyAppointments } from '@/api/appointment'
import { showConfirmDialog } from 'vant'
import dayjs from 'dayjs'

const router = useRouter()
const userStore = useUserStore()

const activeTab = ref(0)
const showUserMenu = ref(false)
const recentAppointments = ref([])

const userActions = [
  { name: '就诊人管理', icon: 'friends' },
  { name: '就诊须知', icon: 'info-o' },
  { name: '退出登录', icon: 'sign', color: '#ee0a24' }
]

const formatDate = (date) => {
  return dayjs(date).format('MM-DD HH:mm')
}

const onTabChange = (index) => {
  const routes = ['/home', '/appointments', '/reports', '']
  if (routes[index]) {
    router.push(routes[index])
  }
}

const onUserAction = (action) => {
  showUserMenu.value = false
  switch (action.name) {
    case '就诊人管理':
      router.push('/patients')
      break
    case '就诊须知':
      router.push('/instructions')
      break
    case '退出登录':
      handleLogout()
      break
  }
}

const handleLogout = async () => {
  try {
    await showConfirmDialog({
      title: '确认退出',
      message: '确定要退出登录吗？'
    })
    userStore.logout()
    router.push('/login')
  } catch {
    // 用户取消
  }
}

const loadRecentAppointments = async () => {
  try {
    const appointments = await getMyAppointments()
    recentAppointments.value = appointments.slice(0, 3) // 只显示最近3条
  } catch (error) {
    console.error('获取预约列表失败:', error)
  }
}

onMounted(() => {
  loadRecentAppointments()
})
</script>

<style scoped>
.home {
  background-color: #f7f8fa;
  min-height: 100vh;
  padding-bottom: 60px;
}

.banner {
  height: 200px;
}

.banner-item {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: white;
  text-align: center;
}

.banner1 {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.banner2 {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.banner-item h2 {
  font-size: 24px;
  margin: 0 0 10px 0;
  font-weight: 300;
}

.banner-item p {
  font-size: 16px;
  margin: 0;
  opacity: 0.9;
}

.menu-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  padding: 30px 20px;
  background: white;
  margin-bottom: 10px;
}

.menu-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  cursor: pointer;
  transition: transform 0.2s;
}

.menu-item:active {
  transform: scale(0.95);
}

.menu-item .van-icon {
  font-size: 32px;
  color: #1989fa;
  margin-bottom: 8px;
}

.menu-item span {
  font-size: 14px;
  color: #323233;
}
</style>
