{"name": "vue-demi", "version": "0.13.11", "engines": {"node": ">=12"}, "repository": "https://github.com/antfu/vue-demi.git", "funding": "https://github.com/sponsors/antfu", "license": "MIT", "author": "<PERSON> <<EMAIL>>", "main": "lib/index.cjs", "jsdelivr": "lib/index.iife.js", "unpkg": "lib/index.iife.js", "module": "lib/index.mjs", "types": "lib/index.d.ts", "exports": {".": {"require": "./lib/index.cjs", "import": "./lib/index.mjs", "browser": "./lib/index.mjs", "types": "./lib/index.d.ts"}, "./*": "./*"}, "bin": {"vue-demi-fix": "bin/vue-demi-fix.js", "vue-demi-switch": "bin/vue-demi-switch.js"}, "files": ["lib", "bin", "scripts"], "scripts": {"postinstall": "node ./scripts/postinstall.js", "release": "npx bumpp --tag --commit --push && npm publish"}, "peerDependencies": {"@vue/composition-api": "^1.0.0-rc.1", "vue": "^3.0.0-0 || ^2.6.0"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}}