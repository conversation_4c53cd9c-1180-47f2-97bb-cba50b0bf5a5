<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智影在线服务系统</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 40px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            text-align: center;
        }
        h1 {
            font-size: 3em;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .subtitle {
            font-size: 1.2em;
            margin-bottom: 40px;
            opacity: 0.9;
        }
        .buttons {
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
            margin: 40px 0;
        }
        .btn {
            padding: 15px 30px;
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 10px;
            color: white;
            text-decoration: none;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }
        .btn:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .status {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            backdrop-filter: blur(10px);
        }
        .status h3 {
            margin-top: 0;
            color: #fff;
        }
        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        .status-item:last-child {
            border-bottom: none;
        }
        .status-ok {
            color: #4ade80;
        }
        .status-error {
            color: #f87171;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>智影在线服务系统</h1>
        <p class="subtitle">医学影像在线预约与报告查看平台</p>
        
        <div class="status">
            <h3>系统状态</h3>
            <div class="status-item">
                <span>前端服务</span>
                <span class="status-ok">✅ 运行中</span>
            </div>
            <div class="status-item">
                <span>后端API</span>
                <span class="status-ok">✅ 运行中</span>
            </div>
            <div class="status-item">
                <span>数据库</span>
                <span class="status-ok">✅ 连接正常</span>
            </div>
        </div>

        <div class="buttons">
            <a href="/admin.html" class="btn">管理后台</a>
            <a href="/mobile.html" class="btn">患者端</a>
            <a href="/test.html" class="btn">系统测试</a>
        </div>

        <div class="status">
            <h3>快速访问</h3>
            <div class="status-item">
                <span>管理后台登录</span>
                <span>admin / 123456</span>
            </div>
            <div class="status-item">
                <span>API地址</span>
                <span>http://localhost:5000</span>
            </div>
            <div class="status-item">
                <span>前端地址</span>
                <span>http://localhost:5173</span>
            </div>
        </div>
    </div>
</body>
</html>
