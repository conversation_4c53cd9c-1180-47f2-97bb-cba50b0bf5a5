import request from '@/utils/request'

// 获取报告列表
export function getReports() {
  return request({
    url: '/reports',
    method: 'get'
  })
}

// 根据就诊人ID获取报告
export function getReportsByPatientId(patientId) {
  return request({
    url: `/reports/patient/${patientId}`,
    method: 'get'
  })
}

// 获取报告详情
export function getReport(id) {
  return request({
    url: `/reports/${id}`,
    method: 'get'
  })
}

// 同步联影报告（后台管理）
export function syncReports() {
  return request({
    url: '/reports/sync',
    method: 'post'
  })
}
