using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using AutoMapper;
using ZyOnlineApi.Data;
using ZyOnlineApi.DTOs;
using ZyOnlineApi.Models;

namespace ZyOnlineApi.Services
{
    public class AppointmentService : IAppointmentService
    {
        private readonly ApplicationDbContext _context;
        private readonly IMapper _mapper;

        public AppointmentService(ApplicationDbContext context, IMapper mapper)
        {
            _context = context;
            _mapper = mapper;
        }

        public async Task<List<AppointmentDto>> GetAppointmentsByWechatOpenIdAsync(string wechatOpenId)
        {
            var appointments = await _context.Appointments
                .Include(a => a.Patient)
                .Include(a => a.Project)
                .Where(a => a.Patient.WechatOpenId == wechatOpenId)
                .OrderByDescending(a => a.AppointmentTime)
                .ToListAsync();

            return appointments.Select(a => new AppointmentDto
            {
                Id = a.Id,
                PatientId = a.PatientId,
                PatientName = a.Patient.Name,
                PatientPhone = a.Patient.Phone,
                ProjectId = a.ProjectId,
                ProjectName = a.Project.Name,
                AppointmentTime = a.AppointmentTime,
                Status = a.Status,
                Remarks = a.Remarks,
                CreatedAt = a.CreatedAt
            }).ToList();
        }

        public async Task<AppointmentDto> GetAppointmentByIdAsync(int id)
        {
            var appointment = await _context.Appointments
                .Include(a => a.Patient)
                .Include(a => a.Project)
                .FirstOrDefaultAsync(a => a.Id == id);

            if (appointment == null) return null;

            return new AppointmentDto
            {
                Id = appointment.Id,
                PatientId = appointment.PatientId,
                PatientName = appointment.Patient.Name,
                PatientPhone = appointment.Patient.Phone,
                ProjectId = appointment.ProjectId,
                ProjectName = appointment.Project.Name,
                AppointmentTime = appointment.AppointmentTime,
                Status = appointment.Status,
                Remarks = appointment.Remarks,
                CreatedAt = appointment.CreatedAt
            };
        }

        public async Task<AppointmentDto> CreateAppointmentAsync(CreateAppointmentDto createAppointmentDto, string wechatOpenId)
        {
            // 验证就诊人是否属于当前用户
            var patient = await _context.Patients
                .FirstOrDefaultAsync(p => p.Id == createAppointmentDto.PatientId && 
                                         p.WechatOpenId == wechatOpenId && p.IsActive);

            if (patient == null)
            {
                throw new InvalidOperationException("就诊人不存在或无权限");
            }

            // 验证项目是否存在
            var project = await _context.Projects
                .FirstOrDefaultAsync(p => p.Id == createAppointmentDto.ProjectId && p.IsActive);

            if (project == null)
            {
                throw new InvalidOperationException("检查项目不存在");
            }

            // 验证预约时间是否可用
            var appointmentDate = createAppointmentDto.AppointmentTime.Date;
            var appointmentTime = createAppointmentDto.AppointmentTime.TimeOfDay;

            var timeSlot = await _context.AvailableTimeSlots
                .FirstOrDefaultAsync(ts => ts.Date.Date == appointmentDate &&
                                          ts.StartTime <= appointmentTime &&
                                          ts.EndTime > appointmentTime &&
                                          ts.IsAvailable);

            if (timeSlot == null)
            {
                throw new InvalidOperationException("该时间段不可预约");
            }

            if (timeSlot.CurrentAppointments >= timeSlot.MaxAppointments)
            {
                throw new InvalidOperationException("该时间段预约已满");
            }

            // 检查同一就诊人是否在同一天已有预约
            var existingAppointment = await _context.Appointments
                .AnyAsync(a => a.PatientId == createAppointmentDto.PatientId &&
                              a.AppointmentTime.Date == appointmentDate &&
                              a.Status != "已取消");

            if (existingAppointment)
            {
                throw new InvalidOperationException("该就诊人在当天已有预约");
            }

            var appointment = _mapper.Map<Appointment>(createAppointmentDto);
            appointment.Status = "待检查";
            appointment.CreatedAt = DateTime.Now;
            appointment.UpdatedAt = DateTime.Now;

            _context.Appointments.Add(appointment);

            // 更新时间段的当前预约数
            timeSlot.CurrentAppointments++;

            await _context.SaveChangesAsync();

            return await GetAppointmentByIdAsync(appointment.Id);
        }

        public async Task<bool> CancelAppointmentAsync(int id, string wechatOpenId)
        {
            var appointment = await _context.Appointments
                .Include(a => a.Patient)
                .FirstOrDefaultAsync(a => a.Id == id && a.Patient.WechatOpenId == wechatOpenId);

            if (appointment == null || appointment.Status != "待检查")
            {
                return false;
            }

            appointment.Status = "已取消";
            appointment.UpdatedAt = DateTime.Now;

            // 更新时间段的当前预约数
            var appointmentDate = appointment.AppointmentTime.Date;
            var appointmentTime = appointment.AppointmentTime.TimeOfDay;

            var timeSlot = await _context.AvailableTimeSlots
                .FirstOrDefaultAsync(ts => ts.Date.Date == appointmentDate &&
                                          ts.StartTime <= appointmentTime &&
                                          ts.EndTime > appointmentTime);

            if (timeSlot != null && timeSlot.CurrentAppointments > 0)
            {
                timeSlot.CurrentAppointments--;
            }

            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<PagedResult<AppointmentDto>> GetAppointmentsAsync(AppointmentQueryDto query)
        {
            var queryable = _context.Appointments
                .Include(a => a.Patient)
                .Include(a => a.Project)
                .AsQueryable();

            // 状态筛选
            if (!string.IsNullOrEmpty(query.Status) && query.Status != "全部")
            {
                queryable = queryable.Where(a => a.Status == query.Status);
            }

            // 日期筛选
            if (query.StartDate.HasValue)
            {
                queryable = queryable.Where(a => a.AppointmentTime.Date >= query.StartDate.Value.Date);
            }

            if (query.EndDate.HasValue)
            {
                queryable = queryable.Where(a => a.AppointmentTime.Date <= query.EndDate.Value.Date);
            }

            // 就诊人姓名筛选
            if (!string.IsNullOrEmpty(query.PatientName))
            {
                queryable = queryable.Where(a => a.Patient.Name.Contains(query.PatientName));
            }

            // 就诊人手机号筛选
            if (!string.IsNullOrEmpty(query.PatientPhone))
            {
                queryable = queryable.Where(a => a.Patient.Phone.Contains(query.PatientPhone));
            }

            var totalCount = await queryable.CountAsync();

            var appointments = await queryable
                .OrderByDescending(a => a.AppointmentTime)
                .Skip((query.PageIndex - 1) * query.PageSize)
                .Take(query.PageSize)
                .ToListAsync();

            var appointmentDtos = appointments.Select(a => new AppointmentDto
            {
                Id = a.Id,
                PatientId = a.PatientId,
                PatientName = a.Patient.Name,
                PatientPhone = a.Patient.Phone,
                ProjectId = a.ProjectId,
                ProjectName = a.Project.Name,
                AppointmentTime = a.AppointmentTime,
                Status = a.Status,
                Remarks = a.Remarks,
                CreatedAt = a.CreatedAt
            }).ToList();

            return new PagedResult<AppointmentDto>
            {
                Items = appointmentDtos,
                TotalCount = totalCount,
                PageIndex = query.PageIndex,
                PageSize = query.PageSize
            };
        }

        public async Task<AppointmentDto> UpdateAppointmentStatusAsync(int id, UpdateAppointmentStatusDto updateStatusDto)
        {
            var appointment = await _context.Appointments
                .Include(a => a.Patient)
                .Include(a => a.Project)
                .FirstOrDefaultAsync(a => a.Id == id);

            if (appointment == null)
            {
                throw new InvalidOperationException("预约不存在");
            }

            appointment.Status = updateStatusDto.Status;
            appointment.Remarks = updateStatusDto.Remarks;
            appointment.UpdatedAt = DateTime.Now;

            await _context.SaveChangesAsync();

            return new AppointmentDto
            {
                Id = appointment.Id,
                PatientId = appointment.PatientId,
                PatientName = appointment.Patient.Name,
                PatientPhone = appointment.Patient.Phone,
                ProjectId = appointment.ProjectId,
                ProjectName = appointment.Project.Name,
                AppointmentTime = appointment.AppointmentTime,
                Status = appointment.Status,
                Remarks = appointment.Remarks,
                CreatedAt = appointment.CreatedAt
            };
        }

        public async Task<List<AvailableTimeSlotDto>> GetAvailableTimeSlotsAsync(DateTime date)
        {
            var timeSlots = await _context.AvailableTimeSlots
                .Where(ts => ts.Date.Date == date.Date && ts.IsAvailable)
                .OrderBy(ts => ts.StartTime)
                .ToListAsync();

            return _mapper.Map<List<AvailableTimeSlotDto>>(timeSlots);
        }

        public async Task UpdateAppointmentStatusesAsync()
        {
            var today = DateTime.Today;
            var expiredAppointments = await _context.Appointments
                .Where(a => a.Status == "待检查" && a.AppointmentTime.Date < today)
                .ToListAsync();

            foreach (var appointment in expiredAppointments)
            {
                appointment.Status = "已完成";
                appointment.UpdatedAt = DateTime.Now;
            }

            if (expiredAppointments.Any())
            {
                await _context.SaveChangesAsync();
            }
        }
    }
}
