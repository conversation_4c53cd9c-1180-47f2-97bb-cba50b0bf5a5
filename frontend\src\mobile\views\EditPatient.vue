<template>
  <div class="edit-patient">
    <van-nav-bar title="编辑就诊人" left-arrow @click-left="$router.back()" />
    
    <div class="content" v-if="patient">
      <van-form @submit="handleSubmit">
        <van-cell-group inset>
          <van-field
            v-model="form.name"
            name="name"
            label="姓名"
            placeholder="请输入真实姓名"
            :rules="nameRules"
          />
          
          <van-field name="gender" label="性别">
            <template #input>
              <van-radio-group v-model="form.gender" direction="horizontal">
                <van-radio name="男">男</van-radio>
                <van-radio name="女">女</van-radio>
              </van-radio-group>
            </template>
          </van-field>
          
          <van-field
            v-model="form.idCard"
            name="idCard"
            label="身份证号"
            placeholder="请输入18位身份证号"
            :rules="idCardRules"
          />
          
          <van-field
            v-model="form.phone"
            name="phone"
            label="手机号"
            placeholder="请输入11位手机号"
            :rules="phoneRules"
          />
        </van-cell-group>
        
        <div class="submit-button">
          <van-button
            round
            block
            type="primary"
            native-type="submit"
            :loading="loading"
          >
            保存修改
          </van-button>
        </div>
      </van-form>
    </div>
    
    <van-loading v-else class="loading" />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { getPatient, updatePatient } from '@/api/patient'
import { showToast } from 'vant'

const route = useRoute()
const router = useRouter()

const loading = ref(false)
const patient = ref(null)
const form = ref({
  name: '',
  gender: '男',
  idCard: '',
  phone: ''
})

// 验证规则
const nameRules = [
  { required: true, message: '请输入姓名' },
  { pattern: /^[\u4e00-\u9fa5]{2,10}$/, message: '请输入2-10位中文姓名' }
]

const idCardRules = [
  { required: true, message: '请输入身份证号' },
  { 
    pattern: /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/, 
    message: '请输入正确的身份证号' 
  }
]

const phoneRules = [
  { required: true, message: '请输入手机号' },
  { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号' }
]

const loadPatient = async () => {
  try {
    const id = route.params.id
    patient.value = await getPatient(id)
    form.value = { ...patient.value }
  } catch (error) {
    console.error('获取就诊人信息失败:', error)
    showToast('获取就诊人信息失败')
    router.back()
  }
}

const handleSubmit = async () => {
  loading.value = true
  try {
    const id = route.params.id
    await updatePatient(id, form.value)
    showToast.success('修改成功')
    router.back()
  } catch (error) {
    console.error('修改失败:', error)
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  loadPatient()
})
</script>

<style scoped>
.edit-patient {
  background-color: #f7f8fa;
  min-height: 100vh;
}

.content {
  padding: 16px;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.submit-button {
  margin-top: 32px;
}
</style>
