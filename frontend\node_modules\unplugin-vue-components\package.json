{"name": "unplugin-vue-components", "version": "0.25.2", "packageManager": "pnpm@8.6.8", "description": "Components auto importing for Vue", "author": "antfu <<EMAIL>>", "license": "MIT", "funding": "https://github.com/sponsors/antfu", "homepage": "https://github.com/antfu/unplugin-vue-components", "repository": {"type": "git", "url": "https://github.com/antfu/unplugin-vue-components"}, "bugs": "https://github.com/antfu/unplugin-vue-components/issues", "exports": {".": {"types": "./dist/index.d.ts", "require": "./dist/index.js", "import": "./dist/index.mjs"}, "./nuxt": {"types": "./dist/nuxt.d.ts", "require": "./dist/nuxt.js", "import": "./dist/nuxt.mjs"}, "./resolvers": {"types": "./dist/resolvers.d.ts", "require": "./dist/resolvers.js", "import": "./dist/resolvers.mjs"}, "./rollup": {"types": "./dist/rollup.d.ts", "require": "./dist/rollup.js", "import": "./dist/rollup.mjs"}, "./types": {"types": "./dist/types.d.ts", "require": "./dist/types.js", "import": "./dist/types.mjs"}, "./vite": {"types": "./dist/vite.d.ts", "require": "./dist/vite.js", "import": "./dist/vite.mjs"}, "./webpack": {"types": "./dist/webpack.d.ts", "require": "./dist/webpack.js", "import": "./dist/webpack.mjs"}, "./rspack": {"types": "./dist/rspack.d.ts", "require": "./dist/rspack.js", "import": "./dist/rspack.mjs"}, "./esbuild": {"types": "./dist/esbuild.d.ts", "require": "./dist/esbuild.js", "import": "./dist/esbuild.mjs"}, "./*": "./*"}, "main": "dist/index.js", "module": "dist/index.mjs", "types": "index.d.ts", "typesVersions": {"*": {"*": ["./dist/*"]}}, "files": ["dist"], "engines": {"node": ">=14"}, "scripts": {"build": "tsup && esno scripts/postbuild.ts", "dev": "tsup --watch src", "example:build": "npm -C examples/vite-vue3 run build", "example:dev": "npm -C examples/vite-vue3 run dev", "prepublishOnly": "npm run build", "lint": "eslint .", "release": "bumpp && npm publish", "test": "vitest", "test:update": "vitest --u"}, "peerDependencies": {"@babel/parser": "^7.15.8", "@nuxt/kit": "^3.2.2", "vue": "2 || 3"}, "peerDependenciesMeta": {"@babel/parser": {"optional": true}, "@nuxt/kit": {"optional": true}}, "dependencies": {"@antfu/utils": "^0.7.5", "@rollup/pluginutils": "^5.0.2", "chokidar": "^3.5.3", "debug": "^4.3.4", "fast-glob": "^3.3.0", "local-pkg": "^0.4.3", "magic-string": "^0.30.1", "minimatch": "^9.0.3", "resolve": "^1.22.2", "unplugin": "^1.4.0"}, "devDependencies": {"@antfu/eslint-config": "^0.39.8", "@babel/parser": "^7.22.7", "@babel/types": "^7.22.5", "@nuxt/kit": "^3.6.3", "@types/debug": "^4.1.8", "@types/minimatch": "^5.1.2", "@types/node": "^20.4.2", "@types/resolve": "^1.20.2", "@typescript-eslint/eslint-plugin": "^6.1.0", "bumpp": "^9.1.1", "compare-versions": "^6.0.0", "element-plus": "^2.3.8", "eslint": "^8.45.0", "esno": "^0.17.0", "estree-walker": "^3.0.3", "pathe": "^1.1.1", "rollup": "^3.26.3", "tsup": "^7.1.0", "typescript": "^5.1.6", "vite": "^4.4.4", "vitest": "^0.33.0", "vue": "3.2.45"}}