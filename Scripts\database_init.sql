-- 智影在线服务数据库初始化脚本

-- 创建数据库
CREATE DATABASE IF NOT EXISTS zyonline_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE zyonline_db;

-- 用户表
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHA<PERSON>(50) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    role VARCHAR(50) NOT NULL,
    real_name VARCHAR(50),
    phone VARCHAR(20),
    is_active BOOLEAN DEFAULT TRUE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 就诊人表
CREATE TABLE IF NOT EXISTS patients (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) NOT NULL,
    gender VARCHAR(10) NOT NULL,
    id_card VARCHAR(18) NOT NULL,
    phone VARCHAR(20) NOT NULL,
    wechat_openid VARCHAR(50) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_wechat_openid (wechat_openid),
    INDEX idx_id_card (id_card)
);

-- 项目表
CREATE TABLE IF NOT EXISTS projects (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) NOT NULL,
    description VARCHAR(200),
    is_active BOOLEAN DEFAULT TRUE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 可预约时间段表
CREATE TABLE IF NOT EXISTS available_time_slots (
    id INT AUTO_INCREMENT PRIMARY KEY,
    date DATE NOT NULL,
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    max_appointments INT DEFAULT 10,
    current_appointments INT DEFAULT 0,
    is_available BOOLEAN DEFAULT TRUE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 预约表
CREATE TABLE IF NOT EXISTS appointments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    patient_id INT NOT NULL,
    project_id INT NOT NULL,
    appointment_time DATETIME NOT NULL,
    status VARCHAR(20) DEFAULT '待检查',
    remarks VARCHAR(500),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (patient_id) REFERENCES patients(id),
    FOREIGN KEY (project_id) REFERENCES projects(id),
    INDEX idx_appointment_time (appointment_time),
    INDEX idx_patient_id (patient_id),
    INDEX idx_status (status)
);

-- 报告表
CREATE TABLE IF NOT EXISTS reports (
    id INT AUTO_INCREMENT PRIMARY KEY,
    patient_id INT NOT NULL,
    report_id VARCHAR(100) NOT NULL UNIQUE,
    title VARCHAR(200) NOT NULL,
    report_date DATETIME NOT NULL,
    report_url VARCHAR(500),
    sync_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (patient_id) REFERENCES patients(id),
    INDEX idx_patient_id (patient_id),
    INDEX idx_report_date (report_date)
);

-- 就诊须知表
CREATE TABLE IF NOT EXISTS instructions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(100) NOT NULL,
    content TEXT NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 插入初始数据

-- 插入默认管理员用户
INSERT INTO users (username, password_hash, role, real_name, is_active) VALUES 
('admin', '$2a$11$rQZJKjKjKjKjKjKjKjKjKOeH8H8H8H8H8H8H8H8H8H8H8H8H8H8H8', 'admin', '系统管理员', TRUE);

-- 插入检查项目
INSERT INTO projects (name, description, is_active) VALUES 
('PET', 'PET检查', TRUE),
('DR', 'DR检查', TRUE),
('CT', 'CT检查', TRUE);

-- 插入默认就诊须知
INSERT INTO instructions (title, content, is_active) VALUES 
('检查前准备', '1. 请提前30分钟到达医院\n2. 携带身份证和相关检查资料\n3. 检查前请勿进食', TRUE),
('注意事项', '1. 检查过程中请配合医生\n2. 如有不适请及时告知\n3. 检查后请在休息区等待', TRUE);

-- 插入示例可预约时间段（本周）
INSERT INTO available_time_slots (date, start_time, end_time, max_appointments, current_appointments, is_available) VALUES 
(CURDATE(), '08:00:00', '09:00:00', 10, 0, TRUE),
(CURDATE(), '09:00:00', '10:00:00', 10, 0, TRUE),
(CURDATE(), '10:00:00', '11:00:00', 10, 0, TRUE),
(CURDATE(), '14:00:00', '15:00:00', 10, 0, TRUE),
(CURDATE(), '15:00:00', '16:00:00', 10, 0, TRUE),
(CURDATE(), '16:00:00', '17:00:00', 10, 0, TRUE),
(DATE_ADD(CURDATE(), INTERVAL 1 DAY), '08:00:00', '09:00:00', 10, 0, TRUE),
(DATE_ADD(CURDATE(), INTERVAL 1 DAY), '09:00:00', '10:00:00', 10, 0, TRUE),
(DATE_ADD(CURDATE(), INTERVAL 1 DAY), '10:00:00', '11:00:00', 10, 0, TRUE),
(DATE_ADD(CURDATE(), INTERVAL 1 DAY), '14:00:00', '15:00:00', 10, 0, TRUE),
(DATE_ADD(CURDATE(), INTERVAL 1 DAY), '15:00:00', '16:00:00', 10, 0, TRUE),
(DATE_ADD(CURDATE(), INTERVAL 1 DAY), '16:00:00', '17:00:00', 10, 0, TRUE);

COMMIT;
