using System;
using System.Collections.Generic;
using System.Security.Claims;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ZyOnlineApi.DTOs;
using ZyOnlineApi.Services;

namespace ZyOnlineApi.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize(Roles = "wechat_user")]
    public class PatientsController : ControllerBase
    {
        private readonly IPatientService _patientService;

        public PatientsController(IPatientService patientService)
        {
            _patientService = patientService;
        }

        /// <summary>
        /// 获取当前用户的就诊人列表
        /// </summary>
        [HttpGet]
        public async Task<ActionResult<ApiResponse<List<PatientDto>>>> GetPatients()
        {
            try
            {
                var wechatOpenId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                var patients = await _patientService.GetPatientsByWechatOpenIdAsync(wechatOpenId);
                return Ok(ApiResponse<List<PatientDto>>.SuccessResult(patients));
            }
            catch (Exception ex)
            {
                return BadRequest(ApiResponse<List<PatientDto>>.ErrorResult($"获取就诊人列表失败: {ex.Message}"));
            }
        }

        /// <summary>
        /// 根据ID获取就诊人信息
        /// </summary>
        [HttpGet("{id}")]
        public async Task<ActionResult<ApiResponse<PatientDto>>> GetPatient(int id)
        {
            try
            {
                var wechatOpenId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                
                // 验证就诊人是否属于当前用户
                if (!await _patientService.ValidatePatientOwnershipAsync(id, wechatOpenId))
                {
                    return Forbid();
                }

                var patient = await _patientService.GetPatientByIdAsync(id);
                if (patient == null)
                {
                    return NotFound(ApiResponse<PatientDto>.ErrorResult("就诊人不存在", 404));
                }

                return Ok(ApiResponse<PatientDto>.SuccessResult(patient));
            }
            catch (Exception ex)
            {
                return BadRequest(ApiResponse<PatientDto>.ErrorResult($"获取就诊人信息失败: {ex.Message}"));
            }
        }

        /// <summary>
        /// 创建新的就诊人
        /// </summary>
        [HttpPost]
        public async Task<ActionResult<ApiResponse<PatientDto>>> CreatePatient([FromBody] CreatePatientDto createPatientDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ApiResponse<PatientDto>.ErrorResult("输入数据验证失败"));
                }

                var wechatOpenId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                var patient = await _patientService.CreatePatientAsync(createPatientDto, wechatOpenId);
                
                return CreatedAtAction(nameof(GetPatient), new { id = patient.Id }, 
                    ApiResponse<PatientDto>.SuccessResult(patient, "就诊人创建成功"));
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(ApiResponse<PatientDto>.ErrorResult(ex.Message));
            }
            catch (Exception ex)
            {
                return BadRequest(ApiResponse<PatientDto>.ErrorResult($"创建就诊人失败: {ex.Message}"));
            }
        }

        /// <summary>
        /// 更新就诊人信息
        /// </summary>
        [HttpPut("{id}")]
        public async Task<ActionResult<ApiResponse<PatientDto>>> UpdatePatient(int id, [FromBody] UpdatePatientDto updatePatientDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ApiResponse<PatientDto>.ErrorResult("输入数据验证失败"));
                }

                var wechatOpenId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                var patient = await _patientService.UpdatePatientAsync(id, updatePatientDto, wechatOpenId);
                
                return Ok(ApiResponse<PatientDto>.SuccessResult(patient, "就诊人信息更新成功"));
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(ApiResponse<PatientDto>.ErrorResult(ex.Message));
            }
            catch (Exception ex)
            {
                return BadRequest(ApiResponse<PatientDto>.ErrorResult($"更新就诊人信息失败: {ex.Message}"));
            }
        }

        /// <summary>
        /// 删除就诊人
        /// </summary>
        [HttpDelete("{id}")]
        public async Task<ActionResult<ApiResponse<bool>>> DeletePatient(int id)
        {
            try
            {
                var wechatOpenId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                var result = await _patientService.DeletePatientAsync(id, wechatOpenId);
                
                if (!result)
                {
                    return NotFound(ApiResponse<bool>.ErrorResult("就诊人不存在", 404));
                }

                return Ok(ApiResponse<bool>.SuccessResult(true, "就诊人删除成功"));
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(ApiResponse<bool>.ErrorResult(ex.Message));
            }
            catch (Exception ex)
            {
                return BadRequest(ApiResponse<bool>.ErrorResult($"删除就诊人失败: {ex.Message}"));
            }
        }
    }
}
