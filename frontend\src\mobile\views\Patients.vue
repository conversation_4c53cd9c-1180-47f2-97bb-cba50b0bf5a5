<template>
  <div class="patients">
    <van-nav-bar title="就诊人管理" left-arrow @click-left="$router.back()" />
    
    <div class="content">
      <!-- 就诊人列表 -->
      <div v-if="patients.length" class="patient-list">
        <div
          v-for="patient in patients"
          :key="patient.id"
          class="patient-card"
          @click="editPatient(patient.id)"
        >
          <div class="patient-info">
            <div class="patient-name">{{ patient.name }}</div>
            <div class="patient-details">
              <span class="gender">{{ patient.gender }}</span>
              <span class="phone">{{ formatPhone(patient.phone) }}</span>
            </div>
            <div class="patient-id">{{ formatIdCard(patient.idCard) }}</div>
          </div>
          <div class="patient-actions">
            <van-icon name="edit" @click.stop="editPatient(patient.id)" />
            <van-icon name="delete" @click.stop="deletePatient(patient)" />
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <van-empty
        v-else
        image="search"
        description="暂无就诊人信息"
      >
        <van-button
          round
          type="primary"
          class="bottom-button"
          @click="addPatient"
        >
          添加就诊人
        </van-button>
      </van-empty>
    </div>

    <!-- 添加按钮 -->
    <div v-if="patients.length" class="add-button">
      <van-button
        round
        type="primary"
        block
        @click="addPatient"
      >
        添加就诊人
      </van-button>
    </div>

    <!-- 删除确认弹窗 -->
    <van-dialog
      v-model:show="showDeleteDialog"
      title="确认删除"
      :message="`确定要删除就诊人"${selectedPatient?.name}"吗？`"
      show-cancel-button
      @confirm="confirmDelete"
    />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { getPatients, deletePatient as deletePatientApi } from '@/api/patient'
import { showToast } from 'vant'

const router = useRouter()

const patients = ref([])
const showDeleteDialog = ref(false)
const selectedPatient = ref(null)

const formatPhone = (phone) => {
  return phone.replace(/(\d{3})(\d{4})(\d{4})/, '$1****$3')
}

const formatIdCard = (idCard) => {
  return idCard.replace(/(\d{6})(\d{8})(\d{4})/, '$1********$3')
}

const loadPatients = async () => {
  try {
    patients.value = await getPatients()
  } catch (error) {
    console.error('获取就诊人列表失败:', error)
  }
}

const addPatient = () => {
  router.push('/patients/add')
}

const editPatient = (id) => {
  router.push(`/patients/edit/${id}`)
}

const deletePatient = (patient) => {
  selectedPatient.value = patient
  showDeleteDialog.value = true
}

const confirmDelete = async () => {
  try {
    await deletePatientApi(selectedPatient.value.id)
    showToast.success('删除成功')
    loadPatients()
  } catch (error) {
    console.error('删除失败:', error)
  }
  showDeleteDialog.value = false
  selectedPatient.value = null
}

onMounted(() => {
  loadPatients()
})
</script>

<style scoped>
.patients {
  background-color: #f7f8fa;
  min-height: 100vh;
}

.content {
  padding: 16px;
}

.patient-list {
  space-y: 12px;
}

.patient-card {
  background: white;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: transform 0.2s;
}

.patient-card:active {
  transform: scale(0.98);
}

.patient-info {
  flex: 1;
}

.patient-name {
  font-size: 18px;
  font-weight: 600;
  color: #323233;
  margin-bottom: 8px;
}

.patient-details {
  display: flex;
  gap: 16px;
  margin-bottom: 4px;
}

.gender {
  color: #1989fa;
  font-weight: 500;
}

.phone {
  color: #646566;
}

.patient-id {
  color: #969799;
  font-size: 14px;
}

.patient-actions {
  display: flex;
  gap: 16px;
}

.patient-actions .van-icon {
  font-size: 20px;
  color: #969799;
  cursor: pointer;
  transition: color 0.2s;
}

.patient-actions .van-icon:hover {
  color: #1989fa;
}

.add-button {
  position: fixed;
  bottom: 20px;
  left: 16px;
  right: 16px;
}

.bottom-button {
  margin-top: 20px;
}
</style>
