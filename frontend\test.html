<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 40px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 4px solid;
        }
        .success {
            background: #f0f9ff;
            border-color: #10b981;
            color: #065f46;
        }
        .error {
            background: #fef2f2;
            border-color: #ef4444;
            color: #991b1b;
        }
        .info {
            background: #f0f9ff;
            border-color: #3b82f6;
            color: #1e40af;
        }
        button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #2563eb;
        }
        #result {
            margin-top: 20px;
            padding: 15px;
            background: #f8fafc;
            border-radius: 5px;
            border: 1px solid #e2e8f0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>智影在线服务系统 - 测试页面</h1>
        
        <div class="status info">
            <strong>系统状态检查</strong>
        </div>

        <div class="status success">
            ✅ 前端服务器: http://localhost:5173 - 运行中
        </div>

        <div class="status success">
            ✅ 后端API服务: http://localhost:5000 - 运行中
        </div>

        <div class="status success">
            ✅ 数据库连接: MySQL - 正常
        </div>

        <h2>功能测试</h2>
        
        <button onclick="testAPI()">测试API连接</button>
        <button onclick="testLogin()">测试登录</button>
        <button onclick="goToAdmin()">进入管理后台</button>
        <button onclick="goToMobile()">进入移动端</button>

        <div id="result"></div>
    </div>

    <script>
        function showResult(message, type = 'info') {
            const result = document.getElementById('result');
            result.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        async function testAPI() {
            try {
                showResult('正在测试API连接...', 'info');
                const response = await fetch('/api/health');
                if (response.ok) {
                    showResult('✅ API连接成功', 'success');
                } else {
                    showResult('❌ API连接失败: ' + response.status, 'error');
                }
            } catch (error) {
                showResult('❌ API连接错误: ' + error.message, 'error');
            }
        }

        async function testLogin() {
            try {
                showResult('正在测试登录...', 'info');
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: '123456'
                    })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    showResult('✅ 登录测试成功: ' + data.message, 'success');
                } else {
                    showResult('❌ 登录测试失败: ' + response.status, 'error');
                }
            } catch (error) {
                showResult('❌ 登录测试错误: ' + error.message, 'error');
            }
        }

        function goToAdmin() {
            window.open('/admin.html', '_blank');
        }

        function goToMobile() {
            window.open('/mobile.html', '_blank');
        }

        // 页面加载时自动检查
        window.onload = function() {
            showResult('页面加载完成，可以开始测试', 'success');
        }
    </script>
</body>
</html>
