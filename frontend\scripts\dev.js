#!/usr/bin/env node

const { spawn } = require('child_process')
const path = require('path')

console.log('🚀 启动智影在线服务前端开发环境...\n')

// 启动 Vite 开发服务器
const viteProcess = spawn('npm', ['run', 'dev'], {
  cwd: path.resolve(__dirname, '..'),
  stdio: 'inherit',
  shell: true
})

viteProcess.on('close', (code) => {
  console.log(`\n开发服务器已停止，退出码: ${code}`)
})

// 处理进程退出
process.on('SIGINT', () => {
  console.log('\n正在停止开发服务器...')
  viteProcess.kill('SIGINT')
})

process.on('SIGTERM', () => {
  console.log('\n正在停止开发服务器...')
  viteProcess.kill('SIGTERM')
})

// 延迟显示访问地址
setTimeout(() => {
  console.log('\n📱 访问地址:')
  console.log('患者端（移动端）: http://localhost:3000/mobile.html')
  console.log('后台管理端: http://localhost:3000/admin.html')
  console.log('\n💡 提示: 请确保后端 API 服务已启动 (http://localhost:5001)')
}, 3000)
