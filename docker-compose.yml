version: '3.8'

services:
  zyonline-api:
    build: .
    ports:
      - "5000:80"
      - "5001:443"
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:80
    depends_on:
      - mysql
    volumes:
      - ./appsettings.json:/app/appsettings.json
    networks:
      - zyonline-network

  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: 123456
      MYSQL_DATABASE: zyonline_db
      MYSQL_USER: zyonline
      MYSQL_PASSWORD: zyonline123
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./Scripts/database_init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - zyonline-network

volumes:
  mysql_data:

networks:
  zyonline-network:
    driver: bridge
