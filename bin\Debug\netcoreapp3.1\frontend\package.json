{"name": "zyonline-frontend", "version": "1.0.0", "description": "智影在线服务前端系统", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore"}, "dependencies": {"@element-plus/icons-vue": "^2.1.0", "axios": "^1.5.0", "dayjs": "^1.11.9", "echarts": "^5.6.0", "element-plus": "^2.3.9", "js-cookie": "^3.0.5", "nprogress": "^0.2.0", "pinia": "^2.1.6", "vant": "^4.6.2", "vue": "^3.3.4", "vue-echarts": "^7.0.3", "vue-router": "^4.2.4"}, "devDependencies": {"@vitejs/plugin-vue": "^4.3.4", "@vue/eslint-config-prettier": "^8.0.0", "eslint": "^8.47.0", "eslint-plugin-vue": "^9.17.0", "prettier": "^3.0.2", "sass": "^1.66.1", "unplugin-auto-import": "^0.16.6", "unplugin-vue-components": "^0.25.2", "vite": "^4.4.9"}}