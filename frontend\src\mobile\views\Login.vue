<template>
  <div class="login-container">
    <div class="login-header">
      <img src="/logo.png" alt="智影在线" class="logo" />
      <h1>智影在线服务</h1>
      <p>天津智影影像</p>
    </div>

    <div class="login-form">
      <van-form @submit="handleLogin">
        <van-cell-group inset>
          <van-field
            v-model="form.openId"
            name="openId"
            label="微信OpenID"
            placeholder="请输入微信OpenID"
            :rules="[{ required: true, message: '请输入微信OpenID' }]"
          />
        </van-cell-group>
        
        <div class="login-btn">
          <van-button
            round
            block
            type="primary"
            native-type="submit"
            :loading="loading"
          >
            登录
          </van-button>
        </div>
      </van-form>
    </div>

    <div class="login-tips">
      <p>注：实际使用时会自动获取微信用户信息</p>
      <p>当前为演示版本，请手动输入OpenID</p>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { showToast } from 'vant'

const router = useRouter()
const userStore = useUserStore()

const loading = ref(false)
const form = ref({
  openId: 'demo_openid_123456'
})

const handleLogin = async () => {
  loading.value = true
  try {
    await userStore.wechatLogin(form.value.openId)
    showToast.success('登录成功')
    router.push('/home')
  } catch (error) {
    console.error('登录失败:', error)
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 60px 20px 20px;
}

.login-header {
  text-align: center;
  color: white;
  margin-bottom: 60px;
}

.logo {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  margin-bottom: 20px;
}

.login-header h1 {
  font-size: 28px;
  margin: 0 0 10px 0;
  font-weight: 300;
}

.login-header p {
  font-size: 16px;
  margin: 0;
  opacity: 0.8;
}

.login-form {
  margin-bottom: 40px;
}

.login-btn {
  margin-top: 30px;
  padding: 0 16px;
}

.login-tips {
  text-align: center;
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
  line-height: 1.5;
}

.login-tips p {
  margin: 5px 0;
}
</style>
