<template>
  <div class="time-slots">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>时间段管理</span>
          <el-button type="primary" @click="showAddDialog = true">
            <el-icon><Plus /></el-icon>
            添加时间段
          </el-button>
        </div>
      </template>

      <!-- 筛选条件 -->
      <div class="filter-section">
        <el-form :inline="true" :model="filterForm" class="filter-form">
          <el-form-item label="日期">
            <el-date-picker
              v-model="filterForm.date"
              type="date"
              placeholder="选择日期"
              @change="loadTimeSlots"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="loadTimeSlots">查询</el-button>
            <el-button @click="resetFilter">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 数据表格 -->
      <el-table
        v-loading="loading"
        :data="timeSlots"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="date" label="日期" width="120">
          <template #default="{ row }">
            {{ formatDate(row.date) }}
          </template>
        </el-table-column>
        <el-table-column prop="startTime" label="开始时间" width="100" />
        <el-table-column prop="endTime" label="结束时间" width="100" />
        <el-table-column prop="maxAppointments" label="最大预约数" width="120" />
        <el-table-column prop="currentAppointments" label="当前预约数" width="120" />
        <el-table-column prop="isAvailable" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.isAvailable ? 'success' : 'danger'">
              {{ row.isAvailable ? '可预约' : '不可预约' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createdAt" label="创建时间" width="160">
          <template #default="{ row }">
            {{ formatDateTime(row.createdAt) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="editTimeSlot(row)">
              编辑
            </el-button>
            <el-button
              :type="row.isAvailable ? 'danger' : 'success'"
              size="small"
              @click="toggleAvailability(row)"
            >
              {{ row.isAvailable ? '禁用' : '启用' }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="loadTimeSlots"
          @current-change="loadTimeSlots"
        />
      </div>
    </el-card>

    <!-- 添加/编辑对话框 -->
    <el-dialog
      v-model="showAddDialog"
      :title="editingTimeSlot ? '编辑时间段' : '添加时间段'"
      width="500px"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="日期" prop="date">
          <el-date-picker
            v-model="form.date"
            type="date"
            placeholder="选择日期"
            style="width: 100%"
          />
        </el-form-item>
        
        <el-form-item label="开始时间" prop="startTime">
          <el-time-picker
            v-model="form.startTime"
            placeholder="选择开始时间"
            format="HH:mm"
            style="width: 100%"
          />
        </el-form-item>
        
        <el-form-item label="结束时间" prop="endTime">
          <el-time-picker
            v-model="form.endTime"
            placeholder="选择结束时间"
            format="HH:mm"
            style="width: 100%"
          />
        </el-form-item>
        
        <el-form-item label="最大预约数" prop="maxAppointments">
          <el-input-number
            v-model="form.maxAppointments"
            :min="1"
            :max="100"
            style="width: 100%"
          />
        </el-form-item>
        
        <el-form-item label="状态" prop="isAvailable">
          <el-switch v-model="form.isAvailable" />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showAddDialog = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          确定
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import dayjs from 'dayjs'

const loading = ref(false)
const submitting = ref(false)
const timeSlots = ref([])
const showAddDialog = ref(false)
const editingTimeSlot = ref(null)

const filterForm = reactive({
  date: null
})

const form = reactive({
  date: null,
  startTime: null,
  endTime: null,
  maxAppointments: 10,
  isAvailable: true
})

const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

const rules = {
  date: [
    { required: true, message: '请选择日期', trigger: 'change' }
  ],
  startTime: [
    { required: true, message: '请选择开始时间', trigger: 'change' }
  ],
  endTime: [
    { required: true, message: '请选择结束时间', trigger: 'change' }
  ],
  maxAppointments: [
    { required: true, message: '请输入最大预约数', trigger: 'blur' }
  ]
}

const formatDate = (date) => {
  return dayjs(date).format('YYYY-MM-DD')
}

const formatDateTime = (dateTime) => {
  return dayjs(dateTime).format('YYYY-MM-DD HH:mm')
}

const loadTimeSlots = async () => {
  loading.value = true
  try {
    // 模拟API调用
    const mockData = [
      {
        id: 1,
        date: '2025-05-26',
        startTime: '08:00',
        endTime: '09:00',
        maxAppointments: 10,
        currentAppointments: 3,
        isAvailable: true,
        createdAt: '2025-05-26 10:00:00'
      },
      {
        id: 2,
        date: '2025-05-26',
        startTime: '09:00',
        endTime: '10:00',
        maxAppointments: 10,
        currentAppointments: 8,
        isAvailable: true,
        createdAt: '2025-05-26 10:00:00'
      }
    ]
    
    timeSlots.value = mockData
    pagination.total = mockData.length
  } catch (error) {
    console.error('获取时间段失败:', error)
    ElMessage.error('获取时间段失败')
  } finally {
    loading.value = false
  }
}

const resetFilter = () => {
  filterForm.date = null
  loadTimeSlots()
}

const editTimeSlot = (timeSlot) => {
  editingTimeSlot.value = timeSlot
  Object.assign(form, {
    ...timeSlot,
    date: new Date(timeSlot.date),
    startTime: new Date(`2000-01-01 ${timeSlot.startTime}`),
    endTime: new Date(`2000-01-01 ${timeSlot.endTime}`)
  })
  showAddDialog.value = true
}

const handleSubmit = async () => {
  submitting.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success(editingTimeSlot.value ? '更新成功' : '添加成功')
    showAddDialog.value = false
    resetForm()
    loadTimeSlots()
  } catch (error) {
    console.error('操作失败:', error)
    ElMessage.error('操作失败')
  } finally {
    submitting.value = false
  }
}

const toggleAvailability = async (timeSlot) => {
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    
    timeSlot.isAvailable = !timeSlot.isAvailable
    ElMessage.success(timeSlot.isAvailable ? '启用成功' : '禁用成功')
  } catch (error) {
    console.error('状态更新失败:', error)
    ElMessage.error('状态更新失败')
  }
}

const resetForm = () => {
  Object.assign(form, {
    date: null,
    startTime: null,
    endTime: null,
    maxAppointments: 10,
    isAvailable: true
  })
  editingTimeSlot.value = null
}

onMounted(() => {
  loadTimeSlots()
})
</script>

<style scoped>
.time-slots {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.filter-section {
  margin-bottom: 20px;
}

.filter-form {
  margin: 0;
}

.pagination-wrapper {
  margin-top: 20px;
  text-align: right;
}
</style>
