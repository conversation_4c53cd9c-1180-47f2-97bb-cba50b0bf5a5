using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using AutoMapper;
using ZyOnlineApi.Data;
using ZyOnlineApi.DTOs;
using ZyOnlineApi.Models;

namespace ZyOnlineApi.Services
{
    public class ReportService : IReportService
    {
        private readonly ApplicationDbContext _context;
        private readonly IMapper _mapper;
        private readonly IConfiguration _configuration;
        private readonly HttpClient _httpClient;

        public ReportService(ApplicationDbContext context, IMapper mapper, IConfiguration configuration, HttpClient httpClient)
        {
            _context = context;
            _mapper = mapper;
            _configuration = configuration;
            _httpClient = httpClient;
        }

        public async Task<List<ReportDto>> GetReportsByWechatOpenIdAsync(string wechatOpenId)
        {
            var reports = await _context.Reports
                .Include(r => r.Patient)
                .Where(r => r.Patient.WechatOpenId == wechatOpenId && r.Patient.IsActive)
                .OrderByDescending(r => r.ReportDate)
                .ToListAsync();

            return _mapper.Map<List<ReportDto>>(reports);
        }

        public async Task<ReportDto> GetReportByIdAsync(int id, string wechatOpenId)
        {
            var report = await _context.Reports
                .Include(r => r.Patient)
                .FirstOrDefaultAsync(r => r.Id == id && r.Patient.WechatOpenId == wechatOpenId && r.Patient.IsActive);

            return _mapper.Map<ReportDto>(report);
        }

        public async Task<List<ReportDto>> GetReportsByPatientIdAsync(int patientId, string wechatOpenId)
        {
            // 验证就诊人是否属于当前用户
            var patient = await _context.Patients
                .FirstOrDefaultAsync(p => p.Id == patientId && p.WechatOpenId == wechatOpenId && p.IsActive);

            if (patient == null)
            {
                throw new UnauthorizedAccessException("无权限访问该就诊人的报告");
            }

            var reports = await _context.Reports
                .Where(r => r.PatientId == patientId)
                .OrderByDescending(r => r.ReportDate)
                .ToListAsync();

            return _mapper.Map<List<ReportDto>>(reports);
        }

        public async Task SyncReportsFromLianYingAsync()
        {
            try
            {
                var lianYingConfig = _configuration.GetSection("LianYingApi");
                var baseUrl = lianYingConfig["BaseUrl"];
                var hospitalCode = lianYingConfig["HospitalCode"];
                var apiKey = lianYingConfig["ApiKey"];

                // 获取所有活跃的就诊人
                var patients = await _context.Patients
                    .Where(p => p.IsActive)
                    .ToListAsync();

                foreach (var patient in patients)
                {
                    try
                    {
                        // 调用联影API获取报告
                        // 这里需要根据实际的联影API文档来实现
                        var requestUrl = $"{baseUrl}/reports?hospitalCode={hospitalCode}&patientIdCard={patient.IdCard}";
                        
                        _httpClient.DefaultRequestHeaders.Clear();
                        _httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {apiKey}");

                        var response = await _httpClient.GetAsync(requestUrl);
                        
                        if (response.IsSuccessStatusCode)
                        {
                            var content = await response.Content.ReadAsStringAsync();
                            // 解析响应并保存报告
                            // 这里需要根据实际的API响应格式来实现
                            await ProcessLianYingReportsAsync(patient.Id, content);
                        }
                    }
                    catch (Exception ex)
                    {
                        // 记录单个患者同步失败的日志
                        Console.WriteLine($"同步患者 {patient.Name} 的报告失败: {ex.Message}");
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"同步联影报告失败: {ex.Message}");
            }
        }

        private async Task ProcessLianYingReportsAsync(int patientId, string responseContent)
        {
            // 这里需要根据联影API的实际响应格式来解析数据
            // 示例实现，实际需要根据API文档调整
            
            // 假设API返回的是JSON格式的报告列表
            // var reportData = JsonSerializer.Deserialize<LianYingReportResponse>(responseContent);
            
            // 示例：创建一个模拟报告
            var existingReport = await _context.Reports
                .FirstOrDefaultAsync(r => r.PatientId == patientId && r.ReportId == "MOCK_REPORT_001");

            if (existingReport == null)
            {
                var report = new Report
                {
                    PatientId = patientId,
                    ReportId = "MOCK_REPORT_001",
                    Title = "示例检查报告",
                    ReportDate = DateTime.Now.AddDays(-7),
                    ReportUrl = "https://example.com/report/001",
                    SyncTime = DateTime.Now,
                    CreatedAt = DateTime.Now
                };

                _context.Reports.Add(report);
                await _context.SaveChangesAsync();
            }
        }
    }

    // 联影API响应模型（需要根据实际API文档定义）
    public class LianYingReportResponse
    {
        public bool Success { get; set; }
        public List<LianYingReport> Data { get; set; }
    }

    public class LianYingReport
    {
        public string ReportId { get; set; }
        public string Title { get; set; }
        public DateTime ReportDate { get; set; }
        public string ReportUrl { get; set; }
    }
}
