# 智影在线服务系统快速测试指南

## 🚀 快速开始

### 前置条件
- ✅ .NET Core 3.1 SDK
- ⚠️ MySQL 8.0+ (可选，用于完整功能测试)
- ✅ Windows/Linux/macOS

### 1. 克隆和构建项目
```bash
# 进入项目目录
cd zyonline_api_test

# 还原依赖
dotnet restore

# 构建项目
dotnet build

# 运行项目
dotnet run --urls=http://localhost:5001
```

### 2. 验证服务启动
打开浏览器访问: http://localhost:5001

应该看到 Swagger API 文档页面。

### 3. 基础API测试

#### 健康检查
```bash
# PowerShell
Invoke-WebRequest -Uri "http://localhost:5001/api/health" -Method GET

# 或使用 curl (Linux/macOS)
curl -X GET http://localhost:5001/api/health
```

**预期响应**:
```json
{
  "success": true,
  "message": "系统运行正常",
  "data": {
    "status": "Healthy",
    "timestamp": "2025-05-26T14:25:23+08:00",
    "version": "1.0.0",
    "environment": "Production"
  },
  "code": 200
}
```

## 📋 核心功能测试清单

### ✅ 无需数据库的测试项目
- [x] 项目编译构建
- [x] 应用程序启动
- [x] Swagger文档访问
- [x] 健康检查API
- [x] API接口结构完整性
- [x] JWT认证配置
- [x] 数据验证规则
- [x] 错误处理机制

### ⚠️ 需要数据库的测试项目
- [ ] 用户登录认证
- [ ] 就诊人CRUD操作
- [ ] 预约管理功能
- [ ] 报告查看功能
- [ ] 时间段管理
- [ ] 就诊须知管理

## 🗄️ 数据库配置（可选）

如果要进行完整功能测试，需要配置MySQL数据库：

### 1. 安装MySQL 8.0+
```bash
# 创建数据库
CREATE DATABASE zyonline_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

# 创建用户（可选）
CREATE USER 'zyonline'@'localhost' IDENTIFIED BY 'zyonline123';
GRANT ALL PRIVILEGES ON zyonline_db.* TO 'zyonline'@'localhost';
FLUSH PRIVILEGES;
```

### 2. 更新连接字符串
编辑 `appsettings.json`:
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=localhost;Database=zyonline_db;Uid=root;Pwd=your_password;CharSet=utf8mb4;"
  }
}
```

### 3. 初始化数据库
```bash
# 执行初始化脚本
mysql -u root -p zyonline_db < Scripts/database_init.sql
```

### 4. 重新启动应用
```bash
dotnet run --urls=http://localhost:5001
```

## 🧪 API测试用例

### 1. 管理员登录测试
```http
POST http://localhost:5001/api/auth/login
Content-Type: application/json

{
  "username": "admin",
  "password": "admin123"
}
```

### 2. 微信用户登录测试
```http
POST http://localhost:5001/api/auth/wechat-login
Content-Type: application/json

{
  "openId": "test_openid_123456"
}
```

### 3. 获取检查项目
```http
GET http://localhost:5001/api/common/projects
Authorization: Bearer {token}
```

### 4. 创建就诊人
```http
POST http://localhost:5001/api/patients
Authorization: Bearer {wechat_token}
Content-Type: application/json

{
  "name": "张三",
  "gender": "男",
  "idCard": "110101199001011234",
  "phone": "13800138000"
}
```

### 5. 创建预约
```http
POST http://localhost:5001/api/appointments
Authorization: Bearer {wechat_token}
Content-Type: application/json

{
  "patientId": 1,
  "projectId": 1,
  "appointmentTime": "2025-01-15T09:00:00",
  "remarks": "首次检查"
}
```

## 🐳 Docker快速部署

### 1. 使用Docker Compose
```bash
# 启动所有服务（包括MySQL）
docker-compose up -d

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

### 2. 仅启动API服务
```bash
# 构建镜像
docker build -t zyonline-api .

# 运行容器
docker run -p 5001:80 zyonline-api
```

## 🔍 故障排除

### 常见问题

#### 1. 端口被占用
```bash
# 查看端口占用
netstat -ano | findstr :5001

# 使用其他端口
dotnet run --urls=http://localhost:5002
```

#### 2. 数据库连接失败
- 检查MySQL服务是否启动
- 验证连接字符串中的用户名密码
- 确认数据库已创建

#### 3. 编译错误
```bash
# 清理项目
dotnet clean

# 重新构建
dotnet build
```

#### 4. 依赖包问题
```bash
# 清理NuGet缓存
dotnet nuget locals all --clear

# 重新还原
dotnet restore
```

## 📊 测试结果验证

### 成功指标
- ✅ 应用程序成功启动
- ✅ Swagger文档可访问
- ✅ 健康检查API返回200状态
- ✅ 无编译错误或警告（除.NET版本警告）

### 性能指标
- 启动时间: < 5秒
- 内存占用: < 100MB
- API响应时间: < 500ms

## 📝 测试报告

完整的测试报告请查看: `Tests/TEST_REPORT.md`

## 🆘 获取帮助

如果遇到问题，请检查：
1. 项目README.md文档
2. 测试报告中的故障排除部分
3. 项目Issues页面
4. 联系开发团队

---

**最后更新**: 2025年5月26日  
**测试环境**: Windows 11, .NET Core 3.1  
**项目版本**: v1.0.0
