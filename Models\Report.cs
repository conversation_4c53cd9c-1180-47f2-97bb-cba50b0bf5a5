using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ZyOnlineApi.Models
{
    [Table("reports")]
    public class Report
    {
        [Key]
        [Column("id")]
        public int Id { get; set; }

        [Required]
        [Column("patient_id")]
        public int PatientId { get; set; }

        [ForeignKey("PatientId")]
        public Patient Patient { get; set; }

        [Required]
        [StringLength(100)]
        [Column("report_id")]
        public string ReportId { get; set; } // 联影系统报告ID

        [Required]
        [StringLength(200)]
        [Column("title")]
        public string Title { get; set; }

        [Column("report_date")]
        public DateTime ReportDate { get; set; }

        [StringLength(500)]
        [Column("report_url")]
        public string ReportUrl { get; set; }

        [Column("sync_time")]
        public DateTime SyncTime { get; set; } = DateTime.Now;

        [Column("created_at")]
        public DateTime CreatedAt { get; set; } = DateTime.Now;
    }
}
