import request from '@/utils/request'

// 获取就诊人列表
export function getPatients() {
  return request({
    url: '/patients',
    method: 'get'
  })
}

// 获取就诊人详情
export function getPatient(id) {
  return request({
    url: `/patients/${id}`,
    method: 'get'
  })
}

// 创建就诊人
export function createPatient(data) {
  return request({
    url: '/patients',
    method: 'post',
    data
  })
}

// 更新就诊人
export function updatePatient(id, data) {
  return request({
    url: `/patients/${id}`,
    method: 'put',
    data
  })
}

// 删除就诊人
export function deletePatient(id) {
  return request({
    url: `/patients/${id}`,
    method: 'delete'
  })
}
