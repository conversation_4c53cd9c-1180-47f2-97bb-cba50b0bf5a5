<template>
  <div class="dashboard">
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon today">
              <el-icon><Calendar /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.todayAppointments }}</div>
              <div class="stats-label">今日预约</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon pending">
              <el-icon><Clock /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.pendingAppointments }}</div>
              <div class="stats-label">待检查</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon completed">
              <el-icon><Check /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.completedAppointments }}</div>
              <div class="stats-label">已完成</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon total">
              <el-icon><User /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.totalPatients }}</div>
              <div class="stats-label">总就诊人</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20" class="charts-row">
      <el-col :span="12">
        <el-card title="预约趋势" class="chart-card">
          <div class="chart-container">
            <div class="chart-placeholder">
              <div class="placeholder-content">
                <el-icon size="48"><TrendCharts /></el-icon>
                <h3>预约趋势图表</h3>
                <p>最近7天预约数量变化</p>
                <div class="trend-data">
                  <div v-for="item in chartData.trendData" :key="item.date" class="trend-item">
                    <span>{{ item.date }}</span>
                    <span>{{ item.count }}个</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="12">
        <el-card title="项目分布" class="chart-card">
          <div class="chart-container">
            <div class="chart-placeholder">
              <div class="placeholder-content">
                <el-icon size="48"><PieChart /></el-icon>
                <h3>项目分布图表</h3>
                <p>各检查项目预约占比</p>
                <div class="project-data">
                  <div v-for="item in chartData.projectData" :key="item.name" class="project-item">
                    <span class="project-color" :style="{ backgroundColor: item.itemStyle.color }"></span>
                    <span>{{ item.name }}</span>
                    <span>{{ item.value }}个</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 最近预约 -->
    <el-card title="最近预约" class="recent-appointments">
      <template #header>
        <div class="card-header">
          <span>最近预约</span>
          <el-button type="text" @click="goToAppointments">
            查看全部
            <el-icon><ArrowRight /></el-icon>
          </el-button>
        </div>
      </template>

      <el-table
        :data="recentAppointments"
        stripe
        :header-cell-style="{ background: '#f8fafc', color: '#475569' }"
        v-loading="loading"
        empty-text="暂无预约数据"
      >
        <el-table-column prop="id" label="预约ID" width="100" align="center">
          <template #default="{ row }">
            <span class="appointment-id">#{{ row.id }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="patientName" label="就诊人" width="120" align="center" />
        <el-table-column prop="patientPhone" label="手机号" width="140" align="center">
          <template #default="{ row }">
            {{ maskPhone(row.patientPhone) }}
          </template>
        </el-table-column>
        <el-table-column prop="projectName" label="检查项目" width="120" align="center" />
        <el-table-column prop="appointmentTime" label="预约时间" width="160" align="center">
          <template #default="{ row }">
            <div class="time-info">
              <div class="date">{{ formatDate(row.appointmentTime) }}</div>
              <div class="time">{{ formatTime(row.appointmentTime) }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)" size="small">
              {{ row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createdAt" label="创建时间" align="center">
          <template #default="{ row }">
            {{ formatDateTime(row.createdAt) }}
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { getAppointments } from '@/api/appointment'
import { Calendar, Clock, Check, User, ArrowRight, TrendCharts, PieChart } from '@element-plus/icons-vue'
import dayjs from 'dayjs'

const router = useRouter()

const stats = reactive({
  todayAppointments: 0,
  pendingAppointments: 0,
  completedAppointments: 0,
  totalPatients: 0
})

const recentAppointments = ref([])
const loading = ref(false)
const chartLoading = ref(false)
const chartData = ref({
  trendData: [],
  projectData: []
})

// 格式化日期时间
const formatDateTime = (dateTime) => {
  return dayjs(dateTime).format('YYYY-MM-DD HH:mm')
}

// 格式化日期
const formatDate = (dateTime) => {
  return dayjs(dateTime).format('MM-DD')
}

// 格式化时间
const formatTime = (dateTime) => {
  return dayjs(dateTime).format('HH:mm')
}

// 手机号脱敏
const maskPhone = (phone) => {
  if (!phone) return ''
  return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
}

// 获取状态类型
const getStatusType = (status) => {
  const typeMap = {
    '待检查': 'warning',
    '已完成': 'success',
    '已取消': 'danger'
  }
  return typeMap[status] || 'info'
}

// 跳转到预约管理页面
const goToAppointments = () => {
  router.push('/dashboard/appointments')
}



const loadDashboardData = async () => {
  try {
    loading.value = true
    chartLoading.value = true

    // 获取最近预约
    const result = await getAppointments({
      pageIndex: 1,
      pageSize: 8 // 只显示8条最近的预约
    })
    recentAppointments.value = result.items

    // 计算统计数据
    const today = dayjs().format('YYYY-MM-DD')
    const allAppointments = await getAppointments({
      pageIndex: 1,
      pageSize: 1000 // 获取更多数据用于统计
    })

    stats.todayAppointments = allAppointments.items.filter(
      item => dayjs(item.appointmentTime).format('YYYY-MM-DD') === today
    ).length

    stats.pendingAppointments = allAppointments.items.filter(
      item => item.status === '待检查'
    ).length

    stats.completedAppointments = allAppointments.items.filter(
      item => item.status === '已完成'
    ).length

    // 统计不重复的就诊人数量
    const uniquePatients = new Set(allAppointments.items.map(item => item.patientId))
    stats.totalPatients = uniquePatients.size

    // 处理图表数据
    processChartData(allAppointments.items)

  } catch (error) {
    console.error('加载仪表板数据失败:', error)
  } finally {
    loading.value = false
    chartLoading.value = false
  }
}

// 处理图表数据
const processChartData = (appointments) => {
  // 处理预约趋势数据（最近7天）
  const trendData = []
  for (let i = 6; i >= 0; i--) {
    const date = dayjs().subtract(i, 'day')
    const dateStr = date.format('YYYY-MM-DD')
    const count = appointments.filter(
      item => dayjs(item.appointmentTime).format('YYYY-MM-DD') === dateStr
    ).length

    trendData.push({
      date: date.format('MM-DD'),
      count: count
    })
  }

  // 处理项目分布数据
  const projectStats = {}
  appointments.forEach(item => {
    const projectName = item.projectName
    if (projectStats[projectName]) {
      projectStats[projectName]++
    } else {
      projectStats[projectName] = 1
    }
  })

  const colors = ['#3498db', '#e74c3c', '#f39c12', '#2ecc71', '#9b59b6', '#1abc9c']
  const projectData = Object.entries(projectStats).map(([name, value], index) => ({
    name,
    value,
    itemStyle: {
      color: colors[index % colors.length]
    }
  }))

  chartData.value = {
    trendData,
    projectData
  }
}

onMounted(() => {
  loadDashboardData()
})
</script>

<style scoped>
.dashboard {
  padding: 0;
  background: transparent;
}

.stats-row {
  margin-bottom: 30px;
}

.stats-card {
  height: 140px;
  border-radius: 16px;
  border: none;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  overflow: hidden;
  position: relative;
}

.stats-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7));
  z-index: 1;
}

.stats-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.stats-card :deep(.el-card__body) {
  padding: 25px;
  height: 100%;
  position: relative;
  z-index: 2;
}

.stats-content {
  display: flex;
  align-items: center;
  height: 100%;
  position: relative;
}

.stats-icon {
  width: 70px;
  height: 70px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  font-size: 28px;
  color: white;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
  position: relative;
  overflow: hidden;
}

.stats-icon::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transform: rotate(45deg);
  animation: shimmer 3s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
  50% { transform: translateX(100%) translateY(100%) rotate(45deg); }
  100% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
}

.stats-icon.today {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stats-icon.pending {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stats-icon.completed {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stats-icon.total {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stats-info {
  flex: 1;
}

.stats-number {
  font-size: 36px;
  font-weight: 700;
  background: linear-gradient(135deg, #2c3e50, #3498db);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  line-height: 1;
  margin-bottom: 8px;
  animation: countUp 1s ease-out;
}

@keyframes countUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.stats-label {
  font-size: 16px;
  color: #7f8c8d;
  font-weight: 500;
  letter-spacing: 0.5px;
}

.charts-row {
  margin-bottom: 30px;
}

.charts-row :deep(.el-card) {
  border-radius: 16px;
  border: none;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.charts-row :deep(.el-card:hover) {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.12);
}

.charts-row :deep(.el-card__header) {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-bottom: 1px solid #e2e8f0;
  padding: 20px 25px;
  font-weight: 600;
  color: #2c3e50;
  font-size: 18px;
}

.charts-row :deep(.el-card__body) {
  padding: 25px;
}

.chart-container {
  height: 320px;
  padding: 20px;
  background: #ffffff;
  border-radius: 12px;
  position: relative;
  overflow: hidden;
}

.chart-placeholder {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-radius: 8px;
  border: 2px dashed #cbd5e0;
}

.placeholder-content {
  text-align: center;
  color: #64748b;
}

.placeholder-content h3 {
  margin: 15px 0 10px 0;
  color: #2c3e50;
  font-size: 18px;
  font-weight: 600;
}

.placeholder-content p {
  margin: 0 0 20px 0;
  color: #94a3b8;
  font-size: 14px;
}

.trend-data, .project-data {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: 15px;
}

.trend-item, .project-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 6px;
  font-size: 13px;
  min-width: 120px;
}

.project-color {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 8px;
}

.chart-card :deep(.el-card__body) {
  padding: 0;
}

.recent-appointments {
  margin-bottom: 30px;
}

.recent-appointments :deep(.el-card) {
  border-radius: 16px;
  border: none;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.recent-appointments :deep(.el-card:hover) {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.12);
}

.recent-appointments :deep(.el-card__header) {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-bottom: 1px solid #e2e8f0;
  padding: 20px 25px;
  font-weight: 600;
  color: #2c3e50;
  font-size: 18px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.card-header span {
  font-weight: 600;
  color: #2c3e50;
  font-size: 18px;
}

.card-header .el-button {
  color: #3498db;
  font-weight: 500;
}

.card-header .el-button:hover {
  color: #2980b9;
}

.appointment-id {
  font-weight: 600;
  color: #3498db;
  font-family: 'Courier New', monospace;
}

.time-info {
  text-align: center;
}

.time-info .date {
  font-weight: 600;
  color: #2c3e50;
  font-size: 14px;
  margin-bottom: 2px;
}

.time-info .time {
  color: #7f8c8d;
  font-size: 12px;
}

.recent-appointments :deep(.el-card__body) {
  padding: 0;
}

.recent-appointments :deep(.el-table) {
  border-radius: 0 0 16px 16px;
  overflow: hidden;
}

.recent-appointments :deep(.el-table__header) {
  background: #f8fafc;
}

.recent-appointments :deep(.el-table th) {
  background: #f8fafc !important;
  color: #475569;
  font-weight: 600;
  border-bottom: 2px solid #e2e8f0;
}

.recent-appointments :deep(.el-table td) {
  border-bottom: 1px solid #f1f5f9;
  padding: 15px 12px;
}

.recent-appointments :deep(.el-table__row:hover) {
  background: #f8fafc !important;
}

.recent-appointments :deep(.el-tag) {
  border-radius: 20px;
  padding: 4px 12px;
  font-weight: 500;
  border: none;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .stats-row :deep(.el-col) {
    margin-bottom: 20px;
  }
}

@media (max-width: 768px) {
  .dashboard {
    padding: 0;
  }

  .stats-card {
    height: 120px;
  }

  .stats-icon {
    width: 60px;
    height: 60px;
    font-size: 24px;
  }

  .stats-number {
    font-size: 28px;
  }

  .stats-label {
    font-size: 14px;
  }

  .chart-container {
    height: 250px;
  }
}

/* 加载动画 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.stats-card {
  animation: fadeInUp 0.6s ease-out;
}

.stats-card:nth-child(1) { animation-delay: 0.1s; }
.stats-card:nth-child(2) { animation-delay: 0.2s; }
.stats-card:nth-child(3) { animation-delay: 0.3s; }
.stats-card:nth-child(4) { animation-delay: 0.4s; }

.charts-row :deep(.el-card) {
  animation: fadeInUp 0.6s ease-out 0.5s both;
}

.recent-appointments {
  animation: fadeInUp 0.6s ease-out 0.6s both;
}
</style>
