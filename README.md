# 智影在线服务 API

## 项目简介

智影在线服务系统是一个基于 .NET Core 3.1 开发的 Web API 项目，为患者端（微信公众号）和后台管理端提供服务。

## 功能模块

### 患者端功能
- 就诊人管理
- 在线预约
- 我的预约
- 我的报告

### 后台管理端功能
- 预约记录管理
- 预约时间管理
- 就诊须知管理
- 报告同步

## 技术栈

- .NET Core 3.1
- Entity Framework Core
- MySQL 数据库
- JWT 身份验证
- AutoMapper
- Swagger API 文档

## 快速开始

### 1. 环境要求
- .NET Core 3.1 SDK
- MySQL 8.0+
- Visual Studio 2019+ 或 VS Code

### 2. 数据库配置
1. 创建 MySQL 数据库 `zyonline_db`
2. 修改 `appsettings.json` 中的数据库连接字符串
3. 运行数据库迁移：
```bash
dotnet ef migrations add InitialCreate
dotnet ef database update
```

### 3. 运行项目
```bash
dotnet restore
dotnet run
```

### 4. 访问 API 文档
项目启动后，访问 `https://localhost:5001` 查看 Swagger API 文档。

## API 接口说明

### 认证接口
- `POST /api/auth/login` - 后台管理员登录
- `POST /api/auth/wechat-login` - 微信用户登录
- `GET /api/auth/me` - 获取当前用户信息

### 就诊人管理接口
- `GET /api/patients` - 获取就诊人列表
- `POST /api/patients` - 创建就诊人
- `PUT /api/patients/{id}` - 更新就诊人信息
- `DELETE /api/patients/{id}` - 删除就诊人

### 预约管理接口
- `GET /api/appointments/my` - 获取我的预约
- `POST /api/appointments` - 创建预约
- `PUT /api/appointments/{id}/cancel` - 取消预约
- `GET /api/appointments` - 获取预约列表（后台）
- `PUT /api/appointments/{id}/status` - 更新预约状态（后台）

### 报告管理接口
- `GET /api/reports` - 获取报告列表
- `GET /api/reports/patient/{patientId}` - 获取指定就诊人的报告
- `POST /api/reports/sync` - 同步联影报告（后台）

### 公共接口
- `GET /api/common/projects` - 获取检查项目
- `GET /api/common/time-slots` - 获取可预约时间段
- `GET /api/common/instructions` - 获取就诊须知

## 数据库表结构

### 用户表 (users)
- id: 主键
- username: 用户名
- password_hash: 密码哈希
- role: 角色（admin/staff）
- real_name: 真实姓名
- phone: 手机号
- is_active: 是否激活
- created_at: 创建时间
- updated_at: 更新时间

### 就诊人表 (patients)
- id: 主键
- name: 姓名
- gender: 性别
- id_card: 身份证号
- phone: 手机号
- wechat_openid: 微信OpenId
- is_active: 是否激活
- created_at: 创建时间
- updated_at: 更新时间

### 预约表 (appointments)
- id: 主键
- patient_id: 就诊人ID
- project_id: 项目ID
- appointment_time: 预约时间
- status: 状态（待检查/已完成/已取消）
- remarks: 备注
- created_at: 创建时间
- updated_at: 更新时间

### 项目表 (projects)
- id: 主键
- name: 项目名称
- description: 项目描述
- is_active: 是否激活
- created_at: 创建时间
- updated_at: 更新时间

### 可预约时间段表 (available_time_slots)
- id: 主键
- date: 日期
- start_time: 开始时间
- end_time: 结束时间
- max_appointments: 最大预约数
- current_appointments: 当前预约数
- is_available: 是否可用
- created_at: 创建时间
- updated_at: 更新时间

### 报告表 (reports)
- id: 主键
- patient_id: 就诊人ID
- report_id: 联影系统报告ID
- title: 报告标题
- report_date: 报告日期
- report_url: 报告链接
- sync_time: 同步时间
- created_at: 创建时间

### 就诊须知表 (instructions)
- id: 主键
- title: 标题
- content: 内容
- is_active: 是否激活
- created_at: 创建时间
- updated_at: 更新时间

## 配置说明

### JWT 配置
在 `appsettings.json` 中配置 JWT 相关参数：
```json
{
  "JwtSettings": {
    "SecretKey": "your-super-secret-key-that-is-at-least-32-characters-long",
    "Issuer": "ZyOnlineApi",
    "Audience": "ZyOnlineApi",
    "ExpirationInMinutes": 1440
  }
}
```

### 联影API配置
```json
{
  "LianYingApi": {
    "BaseUrl": "https://api.lianying.com",
    "HospitalCode": "your_hospital_code",
    "ApiKey": "your_api_key"
  }
}
```

## 部署说明

1. 发布项目：
```bash
dotnet publish -c Release -o ./publish
```

2. 配置生产环境的数据库连接字符串
3. 配置 IIS 或 Nginx 反向代理
4. 确保 MySQL 数据库可访问

## 注意事项

1. 请确保数据库连接字符串正确
2. JWT SecretKey 在生产环境中应使用强密码
3. 联影API集成需要根据实际API文档进行调整
4. 建议在生产环境中启用 HTTPS
5. 定期备份数据库数据

## 联系方式

如有问题，请联系开发团队。
