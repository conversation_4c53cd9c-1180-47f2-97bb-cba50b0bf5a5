/* 强制按钮水平布局 */
.action-buttons {
  display: flex !important;
  flex-direction: row !important;
  gap: 8px !important;
  justify-content: center !important;
  align-items: center !important;
  flex-wrap: nowrap !important;
}

.action-btn {
  margin: 0 !important;
  white-space: nowrap !important;
  display: inline-flex !important;
  align-items: center !important;
  gap: 4px !important;
}

/* 表格操作列样式 */
.el-table .el-table__cell {
  padding: 12px 8px !important;
}

.el-table .el-table__cell .action-buttons {
  display: flex !important;
  flex-direction: row !important;
  gap: 6px !important;
  justify-content: center !important;
  align-items: center !important;
}

.el-table .el-table__cell .el-button--small {
  margin: 0 !important;
  padding: 4px 8px !important;
  font-size: 12px !important;
  white-space: nowrap !important;
}

/* 确保按钮不换行 */
.el-button {
  white-space: nowrap !important;
}

.el-button + .el-button {
  margin-left: 0 !important;
}
