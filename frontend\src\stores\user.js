import { defineStore } from 'pinia'
import Cookies from 'js-cookie'
import { adminLogin, wechatLogin, getCurrentUser } from '@/api/auth'

export const useUserStore = defineStore('user', {
  state: () => ({
    token: Cookies.get('token') || '',
    userInfo: JSON.parse(Cookies.get('userInfo') || '{}'),
    isLoggedIn: false
  }),

  getters: {
    isAdmin: (state) => state.userInfo.role === 'admin',
    isStaff: (state) => state.userInfo.role === 'staff',
    isWechatUser: (state) => state.userInfo.role === 'wechat_user'
  },

  actions: {
    // 设置token
    setToken(token) {
      this.token = token
      Cookies.set('token', token, { expires: 1 }) // 1天过期
    },

    // 设置用户信息
    setUserInfo(userInfo) {
      this.userInfo = userInfo
      this.isLoggedIn = true
      Cookies.set('userInfo', JSON.stringify(userInfo), { expires: 1 })
    },

    // 管理员登录
    async adminLogin(loginForm) {
      try {
        const data = await adminLogin(loginForm)
        this.setToken(data.token)
        this.setUserInfo({
          username: data.username,
          realName: data.realName,
          role: data.role
        })
        return data
      } catch (error) {
        throw error
      }
    },

    // 微信用户登录
    async wechatLogin(openId) {
      try {
        const token = await wechatLogin({ openId })
        this.setToken(token)
        this.setUserInfo({
          openId,
          role: 'wechat_user'
        })
        return token
      } catch (error) {
        throw error
      }
    },

    // 获取用户信息
    async getUserInfo() {
      try {
        const userInfo = await getCurrentUser()
        this.setUserInfo(userInfo)
        return userInfo
      } catch (error) {
        this.logout()
        throw error
      }
    },

    // 登出
    logout() {
      this.token = ''
      this.userInfo = {}
      this.isLoggedIn = false
      Cookies.remove('token')
      Cookies.remove('userInfo')
    }
  }
})
