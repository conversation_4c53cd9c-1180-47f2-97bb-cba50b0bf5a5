using System;
using System.Security.Claims;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ZyOnlineApi.DTOs;
using ZyOnlineApi.Services;

namespace ZyOnlineApi.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class AuthController : ControllerBase
    {
        private readonly IAuthService _authService;

        public AuthController(IAuthService authService)
        {
            _authService = authService;
        }

        /// <summary>
        /// 后台管理员登录
        /// </summary>
        [HttpPost("login")]
        public async Task<ActionResult<ApiResponse<LoginResponseDto>>> Login([FromBody] LoginDto loginDto)
        {
            try
            {
                var result = await _authService.LoginAsync(loginDto);
                return Ok(ApiResponse<LoginResponseDto>.SuccessResult(result, "登录成功"));
            }
            catch (UnauthorizedAccessException ex)
            {
                return Unauthorized(ApiResponse<LoginResponseDto>.ErrorResult(ex.Message, 401));
            }
            catch (Exception ex)
            {
                return BadRequest(ApiResponse<LoginResponseDto>.ErrorResult($"登录失败: {ex.Message}"));
            }
        }

        /// <summary>
        /// 微信用户登录
        /// </summary>
        [HttpPost("wechat-login")]
        public async Task<ActionResult<ApiResponse<string>>> WechatLogin([FromBody] WechatLoginDto wechatLoginDto)
        {
            try
            {
                var token = await _authService.GenerateWechatTokenAsync(wechatLoginDto.OpenId);
                return Ok(ApiResponse<string>.SuccessResult(token, "微信登录成功"));
            }
            catch (Exception ex)
            {
                return BadRequest(ApiResponse<string>.ErrorResult($"微信登录失败: {ex.Message}"));
            }
        }

        /// <summary>
        /// 获取当前用户信息
        /// </summary>
        [HttpGet("me")]
        [Authorize]
        public ActionResult<ApiResponse<object>> GetCurrentUser()
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            var username = User.FindFirst(ClaimTypes.Name)?.Value;
            var role = User.FindFirst(ClaimTypes.Role)?.Value;

            var userInfo = new
            {
                UserId = userId,
                Username = username,
                Role = role
            };

            return Ok(ApiResponse<object>.SuccessResult(userInfo));
        }
    }
}
