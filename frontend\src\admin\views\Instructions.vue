<template>
  <div class="instructions">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>就诊须知管理</span>
          <el-button type="primary" @click="showAddDialog = true">
            <el-icon><Plus /></el-icon>
            添加须知
          </el-button>
        </div>
      </template>

      <!-- 数据表格 -->
      <el-table
        v-loading="loading"
        :data="instructions"
        stripe
        style="width: 100%"
        :header-cell-style="{ background: '#f8fafc', color: '#475569' }"
      >
        <el-table-column prop="id" label="编号" width="80" align="center" />
        <el-table-column prop="title" label="须知标题" width="200" align="center" />
        <el-table-column prop="content" label="须知内容" show-overflow-tooltip />
        <el-table-column prop="isActive" label="启用状态" width="120" align="center">
          <template #default="{ row }">
            <el-tag :type="row.isActive ? 'success' : 'danger'" size="small">
              {{ row.isActive ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createdAt" label="创建时间" width="180" align="center">
          <template #default="{ row }">
            {{ formatDateTime(row.createdAt) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="180" fixed="right" align="center">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-button type="primary" size="small" @click="editInstruction(row)" class="action-btn">
                <el-icon><Edit /></el-icon>
                编辑
              </el-button>
              <el-button
                :type="row.isActive ? 'danger' : 'success'"
                size="small"
                @click="toggleStatus(row)"
                class="action-btn"
              >
                <el-icon v-if="row.isActive"><Close /></el-icon>
                <el-icon v-else><Check /></el-icon>
                {{ row.isActive ? '禁用' : '启用' }}
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper" v-if="pagination.total > 0">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          :prev-text="'上一页'"
          :next-text="'下一页'"
          @size-change="loadInstructions"
          @current-change="loadInstructions"
        />
      </div>
    </el-card>

    <!-- 添加/编辑对话框 -->
    <el-dialog
      v-model="showAddDialog"
      :title="editingInstruction ? '编辑就诊须知' : '添加就诊须知'"
      width="600px"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="80px"
      >
        <el-form-item label="标题" prop="title">
          <el-input v-model="form.title" placeholder="请输入标题" />
        </el-form-item>

        <el-form-item label="内容" prop="content">
          <el-input
            v-model="form.content"
            type="textarea"
            :rows="8"
            placeholder="请输入内容"
          />
        </el-form-item>

        <el-form-item label="状态" prop="isActive">
          <el-switch v-model="form.isActive" />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="showAddDialog = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          确定
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { getInstructions, createInstruction, updateInstruction, deleteInstruction } from '@/api/common'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Edit, Close, Check } from '@element-plus/icons-vue'
import dayjs from 'dayjs'

const loading = ref(false)
const submitting = ref(false)
const instructions = ref([])
const showAddDialog = ref(false)
const editingInstruction = ref(null)

// 分页数据
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

const form = reactive({
  title: '',
  content: '',
  isActive: true
})

const rules = {
  title: [
    { required: true, message: '请输入标题', trigger: 'blur' }
  ],
  content: [
    { required: true, message: '请输入内容', trigger: 'blur' }
  ]
}

const formatDateTime = (dateTime) => {
  return dayjs(dateTime).format('YYYY-MM-DD HH:mm')
}

const loadInstructions = async () => {
  loading.value = true
  try {
    const result = await getInstructions()
    if (Array.isArray(result)) {
      instructions.value = result
      pagination.total = result.length
    } else {
      instructions.value = result.data || []
      pagination.total = result.total || 0
    }
  } catch (error) {
    console.error('获取就诊须知失败:', error)
    ElMessage.error('获取就诊须知失败')
  } finally {
    loading.value = false
  }
}

const editInstruction = (instruction) => {
  editingInstruction.value = instruction
  Object.assign(form, instruction)
  showAddDialog.value = true
}

const handleSubmit = async () => {
  submitting.value = true
  try {
    if (editingInstruction.value) {
      await updateInstruction(editingInstruction.value.id, form)
      ElMessage.success('更新成功')
    } else {
      await createInstruction(form)
      ElMessage.success('添加成功')
    }
    showAddDialog.value = false
    resetForm()
    loadInstructions()
  } catch (error) {
    console.error('操作失败:', error)
  } finally {
    submitting.value = false
  }
}

const toggleStatus = async (instruction) => {
  try {
    const newStatus = !instruction.isActive
    await updateInstruction(instruction.id, {
      ...instruction,
      isActive: newStatus
    })
    ElMessage.success(newStatus ? '启用成功' : '禁用成功')
    loadInstructions()
  } catch (error) {
    console.error('状态更新失败:', error)
  }
}

const resetForm = () => {
  Object.assign(form, {
    title: '',
    content: '',
    isActive: true
  })
  editingInstruction.value = null
}

onMounted(() => {
  loadInstructions()
})
</script>

<style scoped>
.instructions {
  height: 100%;
  padding: 0;
  width: 100%;
}

.instructions :deep(.el-card) {
  height: 100%;
  border-radius: 16px;
  border: none;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.instructions :deep(.el-card__body) {
  padding: 25px;
  height: calc(100% - 80px);
  display: flex;
  flex-direction: column;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 15px;
  margin-bottom: 25px;
}

.card-header span {
  font-size: 20px;
  font-weight: 600;
  background: linear-gradient(135deg, #2c3e50, #3498db);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.card-header .el-button {
  border-radius: 12px;
  font-weight: 500;
  padding: 10px 20px;
  transition: all 0.3s ease;
}

.card-header .el-button--primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.card-header .el-button--primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

/* 表格样式 */
.instructions :deep(.el-table) {
  width: 100% !important;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  border: none;
  flex: 1;
}

.instructions :deep(.el-table__header) {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.instructions :deep(.el-table th) {
  background: transparent !important;
  color: #475569;
  font-weight: 600;
  border-bottom: 2px solid #e2e8f0;
  padding: 15px 12px;
  font-size: 14px;
}

.instructions :deep(.el-table td) {
  border-bottom: 1px solid #f1f5f9;
  padding: 15px 12px;
  font-size: 14px;
}

.instructions :deep(.el-table__row) {
  transition: all 0.3s ease;
}

.instructions :deep(.el-table__row:hover) {
  background: #f8fafc !important;
}

/* 操作按钮样式 */
.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
  flex-wrap: wrap;
}

.action-btn {
  border-radius: 8px !important;
  font-weight: 500 !important;
  padding: 6px 12px !important;
  transition: all 0.3s ease !important;
  display: flex;
  align-items: center;
  gap: 4px;
}

.action-btn.el-button--primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  border: none !important;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3) !important;
}

.action-btn.el-button--primary:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4) !important;
}

.action-btn.el-button--success {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%) !important;
  border: none !important;
  box-shadow: 0 2px 8px rgba(67, 233, 123, 0.3) !important;
}

.action-btn.el-button--success:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 4px 12px rgba(67, 233, 123, 0.4) !important;
}

.action-btn.el-button--danger {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%) !important;
  border: none !important;
  box-shadow: 0 2px 8px rgba(255, 107, 107, 0.3) !important;
}

.action-btn.el-button--danger:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 4px 12px rgba(255, 107, 107, 0.4) !important;
}

/* 分页样式 */
.pagination-wrapper {
  margin-top: 25px;
  text-align: right;
  padding: 20px 0 0 0;
}

.pagination-wrapper :deep(.el-pagination) {
  justify-content: flex-end;
}

.pagination-wrapper :deep(.el-pager li) {
  border-radius: 8px;
  margin: 0 4px;
  transition: all 0.3s ease;
  font-weight: 500;
}

.pagination-wrapper :deep(.el-pager li:hover) {
  background: #667eea;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.pagination-wrapper :deep(.el-pager li.is-active) {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.pagination-wrapper :deep(.btn-prev),
.pagination-wrapper :deep(.btn-next) {
  border-radius: 8px;
  transition: all 0.3s ease;
}

.pagination-wrapper :deep(.btn-prev:hover),
.pagination-wrapper :deep(.btn-next:hover) {
  background: #667eea;
  color: white;
  transform: translateY(-2px);
}

/* 对话框样式 */
.instructions :deep(.el-dialog) {
  border-radius: 16px;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
}

.instructions :deep(.el-dialog__header) {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-bottom: 1px solid #e2e8f0;
  padding: 20px 25px;
  border-radius: 16px 16px 0 0;
}

.instructions :deep(.el-dialog__title) {
  font-weight: 600;
  color: #2c3e50;
  font-size: 18px;
}

.instructions :deep(.el-dialog__body) {
  padding: 25px;
}

.instructions :deep(.el-dialog__footer) {
  padding: 20px 25px;
  border-top: 1px solid #e2e8f0;
  background: #f8fafc;
  border-radius: 0 0 16px 16px;
}

/* 表单样式 */
.instructions :deep(.el-form-item__label) {
  font-weight: 500;
  color: #475569;
}

.instructions :deep(.el-input__wrapper) {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e1e8ed;
  transition: all 0.3s ease;
  padding: 12px 16px;
}

.instructions :deep(.el-input__wrapper:hover) {
  border-color: #667eea;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
}

.instructions :deep(.el-input__wrapper.is-focus) {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.instructions :deep(.el-input__inner) {
  font-size: 16px;
  color: #2c3e50;
  font-weight: 500;
}

.instructions :deep(.el-textarea__inner) {
  border-radius: 12px;
  border: 1px solid #e1e8ed;
  transition: all 0.3s ease;
  padding: 12px 16px;
  font-size: 16px;
  color: #2c3e50;
  font-weight: 500;
}

.instructions :deep(.el-textarea__inner:hover) {
  border-color: #667eea;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
}

.instructions :deep(.el-textarea__inner:focus) {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .instructions {
    padding: 0;
  }

  .card-header {
    flex-direction: column;
    align-items: stretch;
  }

  .card-header .el-button {
    width: 100%;
    margin-top: 10px;
  }

  .action-buttons {
    flex-direction: column;
    gap: 4px;
  }

  .pagination-wrapper {
    text-align: center;
  }
}

/* 加载动画 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.instructions {
  animation: fadeInUp 0.6s ease-out;
}
</style>
