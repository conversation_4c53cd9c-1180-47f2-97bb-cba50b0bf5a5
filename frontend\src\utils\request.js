import axios from 'axios'
import { ElMessage } from 'element-plus'
import { showToast } from 'vant'
import Cookies from 'js-cookie'

// 创建axios实例
const request = axios.create({
  baseURL: '/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
request.interceptors.request.use(
  config => {
    // 添加token
    const token = Cookies.get('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  response => {
    const { data } = response

    // 如果是文件下载，直接返回
    if (response.config.responseType === 'blob') {
      return response
    }

    // 统一的响应格式处理
    if (data.success) {
      return data.data
    } else {
      // 根据环境显示不同的错误提示
      const isAdmin = window.location.pathname.includes('admin')
      if (isAdmin) {
        ElMessage.error(data.message || '操作失败')
      } else {
        showToast(data.message || '操作失败')
      }
      return Promise.reject(new Error(data.message || '操作失败'))
    }
  },
  error => {
    const { response } = error
    let message = '网络错误'

    if (response) {
      switch (response.status) {
        case 401:
          message = '未授权，请重新登录'
          // 清除token并跳转到登录页
          Cookies.remove('token')
          Cookies.remove('userInfo')
          // 根据当前页面跳转到对应的登录页
          if (window.location.pathname.includes('admin')) {
            window.location.href = '/admin.html'
          } else {
            window.location.href = '/mobile.html'
          }
          break
        case 403:
          message = '拒绝访问'
          break
        case 404:
          message = '请求地址不存在'
          break
        case 500:
          message = '服务器内部错误'
          break
        default:
          message = response.data?.message || '请求失败'
      }
    }

    // 根据环境显示不同的错误提示
    const isAdmin = window.location.pathname.includes('admin')
    if (isAdmin) {
      ElMessage.error(message)
    } else {
      showToast(message)
    }

    return Promise.reject(error)
  }
)

export default request
