<template>
  <div class="reports">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>报告管理</span>
          <el-button type="primary" @click="syncReports" :loading="syncing">
            <el-icon><Refresh /></el-icon>
            同步报告
          </el-button>
        </div>
      </template>

      <!-- 筛选条件 -->
      <div class="filter-section">
        <el-form :inline="true" :model="filterForm" class="filter-form">
          <el-form-item label="患者姓名">
            <el-input
              v-model="filterForm.patientName"
              placeholder="请输入患者姓名"
              clearable
            />
          </el-form-item>
          <el-form-item label="报告日期">
            <el-date-picker
              v-model="filterForm.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="loadReports">查询</el-button>
            <el-button @click="resetFilter">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 数据表格 -->
      <el-table
        v-loading="loading"
        :data="reports"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="reportId" label="报告编号" width="150" />
        <el-table-column prop="patientName" label="患者姓名" width="120" />
        <el-table-column prop="patientIdCard" label="身份证号" width="180">
          <template #default="{ row }">
            {{ maskIdCard(row.patientIdCard) }}
          </template>
        </el-table-column>
        <el-table-column prop="title" label="报告标题" min-width="200" show-overflow-tooltip />
        <el-table-column prop="reportDate" label="报告日期" width="120">
          <template #default="{ row }">
            {{ formatDate(row.reportDate) }}
          </template>
        </el-table-column>
        <el-table-column prop="syncTime" label="同步时间" width="160">
          <template #default="{ row }">
            {{ formatDateTime(row.syncTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button 
              type="primary" 
              size="small" 
              @click="viewReport(row)"
              :disabled="!row.reportUrl"
            >
              查看报告
            </el-button>
            <el-button 
              type="danger" 
              size="small" 
              @click="deleteReport(row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="loadReports"
          @current-change="loadReports"
        />
      </div>
    </el-card>

    <!-- 报告查看对话框 -->
    <el-dialog
      v-model="showReportDialog"
      title="查看报告"
      width="80%"
      :close-on-click-modal="false"
    >
      <div class="report-viewer">
        <div class="report-info">
          <h3>{{ currentReport?.title }}</h3>
          <p><strong>患者:</strong> {{ currentReport?.patientName }}</p>
          <p><strong>报告日期:</strong> {{ formatDate(currentReport?.reportDate) }}</p>
          <p><strong>报告编号:</strong> {{ currentReport?.reportId }}</p>
        </div>
        
        <div class="report-content">
          <iframe 
            v-if="currentReport?.reportUrl"
            :src="currentReport.reportUrl"
            width="100%"
            height="600px"
            frameborder="0"
          ></iframe>
          <div v-else class="no-report">
            <el-empty description="暂无报告内容" />
          </div>
        </div>
      </div>
      
      <template #footer>
        <el-button @click="showReportDialog = false">关闭</el-button>
        <el-button 
          type="primary" 
          @click="downloadReport"
          :disabled="!currentReport?.reportUrl"
        >
          下载报告
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Refresh } from '@element-plus/icons-vue'
import dayjs from 'dayjs'

const loading = ref(false)
const syncing = ref(false)
const reports = ref([])
const showReportDialog = ref(false)
const currentReport = ref(null)

const filterForm = reactive({
  patientName: '',
  dateRange: null
})

const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

const formatDate = (date) => {
  return dayjs(date).format('YYYY-MM-DD')
}

const formatDateTime = (dateTime) => {
  return dayjs(dateTime).format('YYYY-MM-DD HH:mm')
}

const maskIdCard = (idCard) => {
  if (!idCard) return ''
  return idCard.replace(/(\d{6})\d{8}(\d{4})/, '$1********$2')
}

const loadReports = async () => {
  loading.value = true
  try {
    // 模拟API调用
    const mockData = [
      {
        id: 1,
        reportId: 'RPT20250526001',
        patientName: '张三',
        patientIdCard: '110101199001011234',
        title: 'PET-CT检查报告',
        reportDate: '2025-05-25',
        reportUrl: 'https://example.com/report1.pdf',
        syncTime: '2025-05-26 10:30:00'
      },
      {
        id: 2,
        reportId: 'RPT20250526002',
        patientName: '李四',
        patientIdCard: '110101199002021234',
        title: 'DR胸部检查报告',
        reportDate: '2025-05-25',
        reportUrl: 'https://example.com/report2.pdf',
        syncTime: '2025-05-26 11:15:00'
      },
      {
        id: 3,
        reportId: 'RPT20250526003',
        patientName: '王五',
        patientIdCard: '110101199003031234',
        title: 'CT腹部检查报告',
        reportDate: '2025-05-24',
        reportUrl: null,
        syncTime: '2025-05-26 09:45:00'
      }
    ]
    
    reports.value = mockData
    pagination.total = mockData.length
  } catch (error) {
    console.error('获取报告失败:', error)
    ElMessage.error('获取报告失败')
  } finally {
    loading.value = false
  }
}

const resetFilter = () => {
  filterForm.patientName = ''
  filterForm.dateRange = null
  loadReports()
}

const syncReports = async () => {
  syncing.value = true
  try {
    // 模拟同步API调用
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    ElMessage.success('报告同步成功')
    loadReports()
  } catch (error) {
    console.error('同步报告失败:', error)
    ElMessage.error('同步报告失败')
  } finally {
    syncing.value = false
  }
}

const viewReport = (report) => {
  currentReport.value = report
  showReportDialog.value = true
}

const downloadReport = () => {
  if (currentReport.value?.reportUrl) {
    window.open(currentReport.value.reportUrl, '_blank')
  }
}

const deleteReport = async (report) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除报告"${report.title}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 模拟删除API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    
    ElMessage.success('删除成功')
    loadReports()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除报告失败:', error)
      ElMessage.error('删除报告失败')
    }
  }
}

onMounted(() => {
  loadReports()
})
</script>

<style scoped>
.reports {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.filter-section {
  margin-bottom: 20px;
}

.filter-form {
  margin: 0;
}

.pagination-wrapper {
  margin-top: 20px;
  text-align: right;
}

.report-viewer {
  max-height: 80vh;
  overflow-y: auto;
}

.report-info {
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 4px;
  margin-bottom: 20px;
}

.report-info h3 {
  margin: 0 0 10px 0;
  color: #303133;
}

.report-info p {
  margin: 5px 0;
  color: #606266;
}

.report-content {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
}

.no-report {
  height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
