import request from '@/utils/request'

// 获取检查项目列表
export function getProjects() {
  return request({
    url: '/common/projects',
    method: 'get'
  })
}

// 获取可预约时间段
export function getTimeSlots(date) {
  return request({
    url: '/common/time-slots',
    method: 'get',
    params: { date }
  })
}

// 创建时间段（后台管理）
export function createTimeSlot(data) {
  return request({
    url: '/common/time-slots',
    method: 'post',
    data
  })
}

// 更新时间段（后台管理）
export function updateTimeSlot(id, data) {
  return request({
    url: `/common/time-slots/${id}`,
    method: 'put',
    data
  })
}

// 删除时间段（后台管理）
export function deleteTimeSlot(id) {
  return request({
    url: `/common/time-slots/${id}`,
    method: 'delete'
  })
}

// 获取就诊须知
export function getInstructions() {
  return request({
    url: '/common/instructions',
    method: 'get'
  })
}

// 获取就诊须知详情（后台管理）
export function getInstruction(id) {
  return request({
    url: `/common/instructions/${id}`,
    method: 'get'
  })
}

// 创建就诊须知（后台管理）
export function createInstruction(data) {
  return request({
    url: '/common/instructions',
    method: 'post',
    data
  })
}

// 更新就诊须知（后台管理）
export function updateInstruction(id, data) {
  return request({
    url: `/common/instructions/${id}`,
    method: 'put',
    data
  })
}

// 删除就诊须知（后台管理）
export function deleteInstruction(id) {
  return request({
    url: `/common/instructions/${id}`,
    method: 'delete'
  })
}
