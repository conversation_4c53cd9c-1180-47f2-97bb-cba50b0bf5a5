using System.ComponentModel.DataAnnotations;

namespace ZyOnlineApi.DTOs
{
    public class PatientDto
    {
        public int Id { get; set; }

        [Required(ErrorMessage = "姓名不能为空")]
        [StringLength(50, ErrorMessage = "姓名长度不能超过50个字符")]
        public string Name { get; set; }

        [Required(ErrorMessage = "性别不能为空")]
        [RegularExpression("^(男|女)$", ErrorMessage = "性别只能是男或女")]
        public string Gender { get; set; }

        [Required(ErrorMessage = "身份证号不能为空")]
        [RegularExpression(@"^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$", 
            ErrorMessage = "身份证号格式不正确")]
        public string IdCard { get; set; }

        [Required(ErrorMessage = "手机号不能为空")]
        [RegularExpression(@"^1[3-9]\d{9}$", ErrorMessage = "手机号格式不正确")]
        public string Phone { get; set; }

        public string WechatOpenId { get; set; }
    }

    public class CreatePatientDto
    {
        [Required(ErrorMessage = "姓名不能为空")]
        [StringLength(50, ErrorMessage = "姓名长度不能超过50个字符")]
        public string Name { get; set; }

        [Required(ErrorMessage = "性别不能为空")]
        [RegularExpression("^(男|女)$", ErrorMessage = "性别只能是男或女")]
        public string Gender { get; set; }

        [Required(ErrorMessage = "身份证号不能为空")]
        [RegularExpression(@"^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$", 
            ErrorMessage = "身份证号格式不正确")]
        public string IdCard { get; set; }

        [Required(ErrorMessage = "手机号不能为空")]
        [RegularExpression(@"^1[3-9]\d{9}$", ErrorMessage = "手机号格式不正确")]
        public string Phone { get; set; }
    }

    public class UpdatePatientDto
    {
        [Required(ErrorMessage = "姓名不能为空")]
        [StringLength(50, ErrorMessage = "姓名长度不能超过50个字符")]
        public string Name { get; set; }

        [Required(ErrorMessage = "性别不能为空")]
        [RegularExpression("^(男|女)$", ErrorMessage = "性别只能是男或女")]
        public string Gender { get; set; }

        [Required(ErrorMessage = "身份证号不能为空")]
        [RegularExpression(@"^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$", 
            ErrorMessage = "身份证号格式不正确")]
        public string IdCard { get; set; }

        [Required(ErrorMessage = "手机号不能为空")]
        [RegularExpression(@"^1[3-9]\d{9}$", ErrorMessage = "手机号格式不正确")]
        public string Phone { get; set; }
    }
}
