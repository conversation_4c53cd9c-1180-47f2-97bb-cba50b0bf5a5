/* 智影在线管理系统 - 全局样式 */

/* 重置和基础样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  background: #f8fafc;
  color: #2c3e50;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#app {
  height: 100vh;
  overflow: hidden;
}

/* Element Plus 组件样式优化 */

/* 卡片组件 */
.el-card {
  border-radius: 16px !important;
  border: none !important;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08) !important;
  transition: all 0.3s ease !important;
}

.el-card:hover {
  transform: translateY(-5px) !important;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.12) !important;
}

.el-card__header {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%) !important;
  border-bottom: 1px solid #e2e8f0 !important;
  padding: 20px 25px !important;
  font-weight: 600 !important;
  color: #2c3e50 !important;
  font-size: 18px !important;
}

.el-card__body {
  padding: 25px !important;
}

/* 按钮组件 */
.el-button {
  border-radius: 12px !important;
  font-weight: 500 !important;
  transition: all 0.3s ease !important;
  border: none !important;
  padding: 12px 20px !important;
}

.el-button--primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3) !important;
}

.el-button--primary:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4) !important;
}

.el-button--success {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%) !important;
  box-shadow: 0 4px 12px rgba(67, 233, 123, 0.3) !important;
}

.el-button--success:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 25px rgba(67, 233, 123, 0.4) !important;
}

.el-button--warning {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%) !important;
  box-shadow: 0 4px 12px rgba(240, 147, 251, 0.3) !important;
}

.el-button--warning:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 25px rgba(240, 147, 251, 0.4) !important;
}

.el-button--danger {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%) !important;
  box-shadow: 0 4px 12px rgba(255, 107, 107, 0.3) !important;
}

.el-button--danger:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4) !important;
}

/* 输入框组件 */
.el-input__wrapper {
  border-radius: 10px !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
  border: 1px solid #e1e8ed !important;
  transition: all 0.3s ease !important;
  padding: 8px 12px !important;
  min-height: 36px !important;
}

.el-input__wrapper:hover {
  border-color: #667eea !important;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15) !important;
}

.el-input__wrapper.is-focus {
  border-color: #667eea !important;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1) !important;
}

.el-input__inner {
  font-size: 14px !important;
  color: #2c3e50 !important;
  font-weight: 500 !important;
  line-height: 1.4 !important;
}

/* 选择框组件 */
.el-select .el-input__wrapper {
  min-height: 36px !important;
}

.el-select__wrapper {
  min-height: 36px !important;
}

/* 日期选择器组件 */
.el-date-editor .el-input__wrapper {
  min-height: 36px !important;
}

.el-date-editor--daterange .el-input__wrapper {
  min-height: 36px !important;
}

/* 文本域组件 */
.el-textarea__inner {
  border-radius: 10px !important;
  border: 1px solid #e1e8ed !important;
  transition: all 0.3s ease !important;
  padding: 8px 12px !important;
  font-size: 14px !important;
  color: #2c3e50 !important;
  font-weight: 500 !important;
  line-height: 1.5 !important;
}

.el-textarea__inner:hover {
  border-color: #667eea !important;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15) !important;
}

.el-textarea__inner:focus {
  border-color: #667eea !important;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1) !important;
}

/* 表单项组件 */
.el-form-item {
  margin-bottom: 16px !important;
}

.el-form-item__label {
  font-size: 14px !important;
  font-weight: 500 !important;
  color: #475569 !important;
  line-height: 36px !important;
}

.el-form--inline .el-form-item {
  margin-right: 16px !important;
  margin-bottom: 12px !important;
}

/* 表格组件 */
.el-table {
  border-radius: 16px !important;
  overflow: hidden !important;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08) !important;
}

.el-table__header {
  background: #f8fafc !important;
}

.el-table th {
  background: #f8fafc !important;
  color: #475569 !important;
  font-weight: 600 !important;
  border-bottom: 2px solid #e2e8f0 !important;
  padding: 15px 12px !important;
}

.el-table td {
  border-bottom: 1px solid #f1f5f9 !important;
  padding: 15px 12px !important;
}

.el-table__row:hover {
  background: #f8fafc !important;
}

/* 标签组件 */
.el-tag {
  border-radius: 20px !important;
  padding: 4px 12px !important;
  font-weight: 500 !important;
  border: none !important;
}

.el-tag--success {
  background: linear-gradient(135deg, #43e97b, #38f9d7) !important;
  color: white !important;
}

.el-tag--warning {
  background: linear-gradient(135deg, #f093fb, #f5576c) !important;
  color: white !important;
}

.el-tag--danger {
  background: linear-gradient(135deg, #ff6b6b, #ee5a52) !important;
  color: white !important;
}

.el-tag--info {
  background: linear-gradient(135deg, #667eea, #764ba2) !important;
  color: white !important;
}

/* 分页组件 */
.el-pagination {
  margin-top: 20px !important;
  text-align: right !important;
}

.el-pagination .el-pager li {
  border-radius: 8px !important;
  margin: 0 4px !important;
  transition: all 0.3s ease !important;
}

.el-pagination .el-pager li:hover {
  background: #667eea !important;
  color: white !important;
  transform: translateY(-2px) !important;
}

.el-pagination .el-pager li.is-active {
  background: linear-gradient(135deg, #667eea, #764ba2) !important;
  color: white !important;
}

/* 对话框组件 */
.el-dialog {
  border-radius: 16px !important;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15) !important;
}

.el-dialog__header {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%) !important;
  border-bottom: 1px solid #e2e8f0 !important;
  padding: 20px 25px !important;
  border-radius: 16px 16px 0 0 !important;
}

.el-dialog__title {
  font-weight: 600 !important;
  color: #2c3e50 !important;
  font-size: 18px !important;
}

.el-dialog__body {
  padding: 25px !important;
}

.el-dialog__footer {
  padding: 20px 25px !important;
  border-top: 1px solid #e2e8f0 !important;
  background: #f8fafc !important;
  border-radius: 0 0 16px 16px !important;
}

/* 消息提示组件 */
.el-message {
  border-radius: 12px !important;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
  border: none !important;
  padding: 15px 20px !important;
}

.el-message--success {
  background: linear-gradient(135deg, #43e97b, #38f9d7) !important;
  color: white !important;
}

.el-message--warning {
  background: linear-gradient(135deg, #f093fb, #f5576c) !important;
  color: white !important;
}

.el-message--error {
  background: linear-gradient(135deg, #ff6b6b, #ee5a52) !important;
  color: white !important;
}

.el-message--info {
  background: linear-gradient(135deg, #667eea, #764ba2) !important;
  color: white !important;
}

/* 加载组件 */
.el-loading-mask {
  background: rgba(255, 255, 255, 0.9) !important;
  backdrop-filter: blur(10px) !important;
}

.el-loading-spinner {
  color: #667eea !important;
}

/* 工具类 */
.text-gradient {
  background: linear-gradient(135deg, #2c3e50, #3498db);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.shadow-card {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
  border-radius: 16px;
  transition: all 0.3s ease;
}

.shadow-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.12);
}

.gradient-bg {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.fade-in {
  animation: fadeIn 0.6s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, #bdc3c7, #95a5a6);
  border-radius: 3px;
  transition: background 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, #95a5a6, #7f8c8d);
}

/* 响应式工具类 */
@media (max-width: 768px) {
  .mobile-hidden {
    display: none !important;
  }

  .mobile-full {
    width: 100% !important;
  }
}

@media (min-width: 769px) {
  .desktop-hidden {
    display: none !important;
  }
}
