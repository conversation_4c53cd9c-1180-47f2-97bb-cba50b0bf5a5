using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ZyOnlineApi.DTOs;
using ZyOnlineApi.Services;

namespace ZyOnlineApi.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class CommonController : ControllerBase
    {
        private readonly ICommonService _commonService;

        public CommonController(ICommonService commonService)
        {
            _commonService = commonService;
        }

        /// <summary>
        /// 获取所有可用的检查项目
        /// </summary>
        [HttpGet("projects")]
        [Authorize]
        public async Task<ActionResult<ApiResponse<List<ProjectDto>>>> GetProjects()
        {
            try
            {
                var projects = await _commonService.GetActiveProjectsAsync();
                return Ok(ApiResponse<List<ProjectDto>>.SuccessResult(projects));
            }
            catch (Exception ex)
            {
                return BadRequest(ApiResponse<List<ProjectDto>>.ErrorResult($"获取检查项目失败: {ex.Message}"));
            }
        }

        /// <summary>
        /// 获取指定日期的可预约时间段
        /// </summary>
        [HttpGet("time-slots")]
        [Authorize]
        public async Task<ActionResult<ApiResponse<List<AvailableTimeSlotDto>>>> GetTimeSlots([FromQuery] DateTime date)
        {
            try
            {
                var timeSlots = await _commonService.GetAvailableTimeSlotsAsync(date);
                return Ok(ApiResponse<List<AvailableTimeSlotDto>>.SuccessResult(timeSlots));
            }
            catch (Exception ex)
            {
                return BadRequest(ApiResponse<List<AvailableTimeSlotDto>>.ErrorResult($"获取时间段失败: {ex.Message}"));
            }
        }

        /// <summary>
        /// 创建时间段（后台管理端）
        /// </summary>
        [HttpPost("time-slots")]
        [Authorize(Roles = "admin,staff")]
        public async Task<ActionResult<ApiResponse<AvailableTimeSlotDto>>> CreateTimeSlot([FromBody] AvailableTimeSlotDto timeSlotDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ApiResponse<AvailableTimeSlotDto>.ErrorResult("输入数据验证失败"));
                }

                var timeSlot = await _commonService.CreateTimeSlotAsync(timeSlotDto);
                return CreatedAtAction(nameof(GetTimeSlots), new { date = timeSlot.Date }, 
                    ApiResponse<AvailableTimeSlotDto>.SuccessResult(timeSlot, "时间段创建成功"));
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(ApiResponse<AvailableTimeSlotDto>.ErrorResult(ex.Message));
            }
            catch (Exception ex)
            {
                return BadRequest(ApiResponse<AvailableTimeSlotDto>.ErrorResult($"创建时间段失败: {ex.Message}"));
            }
        }

        /// <summary>
        /// 更新时间段（后台管理端）
        /// </summary>
        [HttpPut("time-slots/{id}")]
        [Authorize(Roles = "admin,staff")]
        public async Task<ActionResult<ApiResponse<AvailableTimeSlotDto>>> UpdateTimeSlot(int id, [FromBody] AvailableTimeSlotDto timeSlotDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ApiResponse<AvailableTimeSlotDto>.ErrorResult("输入数据验证失败"));
                }

                var timeSlot = await _commonService.UpdateTimeSlotAsync(id, timeSlotDto);
                return Ok(ApiResponse<AvailableTimeSlotDto>.SuccessResult(timeSlot, "时间段更新成功"));
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(ApiResponse<AvailableTimeSlotDto>.ErrorResult(ex.Message));
            }
            catch (Exception ex)
            {
                return BadRequest(ApiResponse<AvailableTimeSlotDto>.ErrorResult($"更新时间段失败: {ex.Message}"));
            }
        }

        /// <summary>
        /// 删除时间段（后台管理端）
        /// </summary>
        [HttpDelete("time-slots/{id}")]
        [Authorize(Roles = "admin,staff")]
        public async Task<ActionResult<ApiResponse<bool>>> DeleteTimeSlot(int id)
        {
            try
            {
                var result = await _commonService.DeleteTimeSlotAsync(id);
                if (!result)
                {
                    return NotFound(ApiResponse<bool>.ErrorResult("时间段不存在", 404));
                }

                return Ok(ApiResponse<bool>.SuccessResult(true, "时间段删除成功"));
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(ApiResponse<bool>.ErrorResult(ex.Message));
            }
            catch (Exception ex)
            {
                return BadRequest(ApiResponse<bool>.ErrorResult($"删除时间段失败: {ex.Message}"));
            }
        }

        /// <summary>
        /// 获取就诊须知
        /// </summary>
        [HttpGet("instructions")]
        public async Task<ActionResult<ApiResponse<List<InstructionDto>>>> GetInstructions()
        {
            try
            {
                var instructions = await _commonService.GetActiveInstructionsAsync();
                return Ok(ApiResponse<List<InstructionDto>>.SuccessResult(instructions));
            }
            catch (Exception ex)
            {
                return BadRequest(ApiResponse<List<InstructionDto>>.ErrorResult($"获取就诊须知失败: {ex.Message}"));
            }
        }

        /// <summary>
        /// 获取就诊须知详情（后台管理端）
        /// </summary>
        [HttpGet("instructions/{id}")]
        [Authorize(Roles = "admin,staff")]
        public async Task<ActionResult<ApiResponse<InstructionDto>>> GetInstruction(int id)
        {
            try
            {
                var instruction = await _commonService.GetInstructionByIdAsync(id);
                if (instruction == null)
                {
                    return NotFound(ApiResponse<InstructionDto>.ErrorResult("就诊须知不存在", 404));
                }

                return Ok(ApiResponse<InstructionDto>.SuccessResult(instruction));
            }
            catch (Exception ex)
            {
                return BadRequest(ApiResponse<InstructionDto>.ErrorResult($"获取就诊须知失败: {ex.Message}"));
            }
        }

        /// <summary>
        /// 创建就诊须知（后台管理端）
        /// </summary>
        [HttpPost("instructions")]
        [Authorize(Roles = "admin,staff")]
        public async Task<ActionResult<ApiResponse<InstructionDto>>> CreateInstruction([FromBody] InstructionDto instructionDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ApiResponse<InstructionDto>.ErrorResult("输入数据验证失败"));
                }

                var instruction = await _commonService.CreateInstructionAsync(instructionDto);
                return CreatedAtAction(nameof(GetInstruction), new { id = instruction.Id }, 
                    ApiResponse<InstructionDto>.SuccessResult(instruction, "就诊须知创建成功"));
            }
            catch (Exception ex)
            {
                return BadRequest(ApiResponse<InstructionDto>.ErrorResult($"创建就诊须知失败: {ex.Message}"));
            }
        }

        /// <summary>
        /// 更新就诊须知（后台管理端）
        /// </summary>
        [HttpPut("instructions/{id}")]
        [Authorize(Roles = "admin,staff")]
        public async Task<ActionResult<ApiResponse<InstructionDto>>> UpdateInstruction(int id, [FromBody] InstructionDto instructionDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ApiResponse<InstructionDto>.ErrorResult("输入数据验证失败"));
                }

                var instruction = await _commonService.UpdateInstructionAsync(id, instructionDto);
                return Ok(ApiResponse<InstructionDto>.SuccessResult(instruction, "就诊须知更新成功"));
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(ApiResponse<InstructionDto>.ErrorResult(ex.Message));
            }
            catch (Exception ex)
            {
                return BadRequest(ApiResponse<InstructionDto>.ErrorResult($"更新就诊须知失败: {ex.Message}"));
            }
        }

        /// <summary>
        /// 删除就诊须知（后台管理端）
        /// </summary>
        [HttpDelete("instructions/{id}")]
        [Authorize(Roles = "admin,staff")]
        public async Task<ActionResult<ApiResponse<bool>>> DeleteInstruction(int id)
        {
            try
            {
                var result = await _commonService.DeleteInstructionAsync(id);
                if (!result)
                {
                    return NotFound(ApiResponse<bool>.ErrorResult("就诊须知不存在", 404));
                }

                return Ok(ApiResponse<bool>.SuccessResult(true, "就诊须知删除成功"));
            }
            catch (Exception ex)
            {
                return BadRequest(ApiResponse<bool>.ErrorResult($"删除就诊须知失败: {ex.Message}"));
            }
        }
    }
}
